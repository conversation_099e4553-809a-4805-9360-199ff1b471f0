import os

class SotrueAppConfig:

    bank_email="prithvir<PERSON><PERSON><PERSON>@sotrue.website"
    accounts_email="<EMAIL>"

    python_exe=None

    base_url="http://localhost:8000"

    BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    report_temp_path = os.path.join(BASE_DIR, 'sotrueapp', 'media', 'reporttemp') + os.sep
    report_temp_url = '/sotrueapp/media/reporttemp/'

    media_file_url = '/sotrueapp/appservice?_action_code=GET_MEDIA_DATA&media_key='
    missing_media_file = os.path.join(BASE_DIR, 'sotrueapp', 'media', 'MissingMedia.png')

    media_base_path = os.path.join(BASE_DIR, 'sotrueapp', 'media') + os.sep
    media_path = os.path.join(BASE_DIR, 'sotrueapp', 'media', 'media_files') + os.sep
    media_url = '/sotrueapp/static/media_files/'

    profile_path = os.path.join(BASE_DIR, 'sotrueapp', 'media', 'profile_images') + os.sep
    profile_url = '/sotrueapp/static/profile_images/'

    report_path = os.path.join(BASE_DIR, 'sotrueapp', 'media', 'report_images') + os.sep

    verification_docs = os.path.join(BASE_DIR, 'sotrueapp', 'media', 'verification_docs') + os.sep

    default_video_image = os.path.join(BASE_DIR, 'sotrueapp', 'media', 'default_video_image.jpg')

    watermark_file = os.path.join(BASE_DIR, 'sotrueapp', 'media', 'watermark.png')

    media_expiry_mins=15

    servers = {"SERVER1":"https://www.sotrue-server1.in/sotrueapp/views/index.html",
               "SERVER2":"https://www.sotrue-server2.in/sotrueapp/views/index/html"}

    allowed_servers = ["SERVER1"]

    remote_servers = [{"url":"https://www.sotrue-server2.in/sotrueapp/adminservice",
                       "user_id":"<EMAIL>",
                        "password":"A123456#"}
                      ]

    feedback_url = "http://127.0.0.1:8000/sotrueapp/views/index.html#/userFeedback/"

    test_server_msg="TEST SERVER: "