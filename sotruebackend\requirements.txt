asgiref>=3.5.0
attrs>=21.2.0
Automat>=20.2.0
# Remove awscli pin to resolve dependency conflicts
boto3>=1.26.25
# Updated botocore to match boto3
python-dotenv
botocore>=1.29.14
CacheControl>=0.14.0
cachetools>=5.2.0
certifi>=2020.6.20
chardet>=4.0.0
click>=8.0.3
colorama>=0.4.4
configobj>=5.0.6
constantly>=15.1.0
cryptography>=3.4.8
decorator>=4.4.2
distro>=1.7.0
Django>=3.2.12
docutils>=0.16
et-xmlfile>=1.1.0
ffmpeg-python>=0.2.0
firebase-admin==6.2.0
future>=0.18.3
gevent>=22.10.2
google-api-core>=2.11.0
google-api-python-client>=2.68.0
google-auth>=2.35.0
google-auth-httplib2>=0.1.0
google-cloud-core>=2.4.1
google-cloud-firestore>=2.19.0
google-cloud-storage>=2.14.0
google-crc32c>=1.6.0
google-resumable-media>=2.7.2
googleapis-common-protos>=1.57.0
greenlet>=2.0.1
grpcio>=1.66.2
grpcio-status>=1.62.3
gunicorn>=20.1.0
httplib2>=0.20.2
hyperlink>=21.0.0
idna>=2.5
imageio>=2.22.4
imageio-ffmpeg==0.4.5
importlib-metadata>=4.6.4
incremental>=21.3.0
Jinja2>=3.0.3
jmespath>=1.0.1
jsonpatch>=1.32
jsonpointer>=2.0
jsonschema>=3.2.0
keyring>=23.5.0
MarkupSafe>=2.0.1
mixpanel>=4.10.0
more-itertools>=8.10.0
moviepy==2.1.2
msgpack>=1.1.0
# netifaces>=0.11.0
numpy>=1.24.0
oauthlib>=3.2.0
olefile>=0.46
opencv-python-headless>=4.6.0.66
openpyxl>=3.0.10
pexpect>=4.8.0
Pillow>=9.3.0
proglog>=0.1.10
proto-plus>=1.24.0
protobuf==4.21.6
psycopg2-binary>=2.9.5
ptyprocess>=0.7.0
pyasn1>=0.4.8
pyasn1-modules>=0.2.1
Pygments>=2.11.2
# PyGObject removed/commented unless needed
PyHamcrest>=2.0.2
PyJWT>=2.9.0
pyOpenSSL>=21.0.0
pyparsing>=2.4.7
pyrsistent>=0.18.1
pyserial>=3.5
python-dateutil>=2.8.2
python-magic>=0.4.24
pytz>=2022.1
PyYAML>=5.4.1
razorpay>=1.3.0
requests>=2.25.1,<3
roman>=3.3
rsa>=4.9
s3transfer>=0.6.0
SecretStorage>=3.3.1
service-identity>=18.1.0
six>=1.16.0
sqlparse>=0.4.2
tqdm>=4.64.1
Twisted>=22.1.0
uritemplate>=4.1.1
urllib3>=1.26.5
validators>=0.20.0
xlrd>=2.0.1
zipp>=1.0.0
zope.event>=4.5.0
zope.interface>=5.4.0
