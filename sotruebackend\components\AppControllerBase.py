import urllib
import inspect
from django.http import HttpResponse
from django.http import FileResponse
from django.views import View
from django.shortcuts import redirect
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from .AppLogger import AppLogger
from .AppUtils import AppUtils
from .AppSessionMgmt import AppSessionMgmt
from .AppAccessControl import AppAccessControl
from .AppDBManager import AppDBManager
from .AppConfigs import AppConfigs
from .AppSecurity import AppSecurity

"""
The base controller for the application
"""

@method_decorator(csrf_exempt, name='dispatch')
class AppControllerBase(View):

    query_path = "/application/queries/" # Default, do not change unless sure
    session_reset_actions = ["GET_NOTIFICATION_COUNT"]

    def __init__(self):        
        super().__init__()
        AppLogger.get_instance()
        self.logger = AppLogger
        self.utils = AppUtils        
        
        self.configs = AppConfigs
        self.configs.app_name = self.get_app_name()
        
        self.message = self.get_message_mapper()        

                
    def get(self, request, *args, **kwargs):
        return self.process_request(request)        

    def post(self, request, *args, **kwargs):
        return self.process_request(request)        

    def process_request(self,request):
        self.utils.set_time_zone()
        
        self.session = AppSessionMgmt(request.session)
        self.session.delete_session_value("_last_seq")
        self.session.delete_session_value("_action_code")

        if 'HTTP_X_REAL_IP' in request.META:
            self.session.set_session_value("_ip_address",request.META['HTTP_X_REAL_IP'])
        else:
            self.session.set_session_value("_ip_address",request.META['REMOTE_ADDR'])
        
        query_str = request.META.get("QUERY_STRING")
        params = None        
        if len(request.FILES) or request.method == 'GET':    
            params = self.get_params(query_str)
            files = self.process_files(request.FILES)
            if len(files):
                params["uploads"] = files
        else:
            params = self.get_params(query_str,request.body,request.POST)

        if "uploads" not in params:
            params["uploads"] = None

        action_code = params.get("_action_code","")
        print("Action code:",action_code)
        _access_key = params.get("_access_key","")
        print("Action code:",_access_key)

        #if self.is_service_controller():
        #    self.session.add_access_key("hdEdzS9Om6jbVjfOklo11EDu1k8yKbW3")
        
        if len(action_code.strip()) == 0:
            response = dict(status="FAILED",err_cd="_E001_",msg=self.message.get_message("_E001_"))
            return HttpResponse(self.utils.to_json(response))          
        
        if action_code == "GET_SERVICE_PATHS":
            response = self.get_service_paths()
            return HttpResponse(self.utils.to_json(response))

        if action_code[2] == ":":        
            action_code = action_code[3:]
        
        query_path = self.utils.get_base_path() + "/" + self.get_app_name() + self.query_path
        self.connDB = AppDBManager(query_path,self.session)
        self.audit = self.get_auditor(self.session,self.connDB)
        self.access_ctrl = AppAccessControl(self.get_action_overrides(),self.session,self.audit,self.connDB)
        self.session.set_db(self.connDB)

        if self.is_service_controller() and (action_code not in self.get_action_overrides()):
            valid_resp = self.get_validator().do_validation(self.get_action_overrides()[0],params,self.message,self.configs)
            if valid_resp is None:
                self.session.set_session_value("_skip_access_key","TRUE")
                response = self.handle_action(self.get_action_overrides()[0],params)
                json_data = self.utils.parse_json(response)            
                if json_data["status"] != "SUCCESS":                
                    return HttpResponse(response)
            else:
                response = dict(status="FAILED",err_cd="E006",msg=self.message.get_message("_E003_"),data=valid_resp)
                return HttpResponse(self.utils.to_json(response))
            
        if self.access_ctrl.is_access_allowed(action_code,_access_key):

            if action_code not in self.session_reset_actions:                
                self.session.reset_user_session()
                self.session.set_session_value("_session_expiry",AppConfigs.session_expiry)
            else:
                expiry = self.session.get_session_value("_session_expiry")
                if (expiry - 60) <= 0:
                    self.session.end_user_session()                
                self.session.set_session_value("_session_expiry",expiry-60)                                            
            
            valid_resp = self.get_validator().do_validation(action_code,params,self.message,self.configs)

            if valid_resp is None:                
                response = self.handle_action(action_code,params)
                is_raw_response = self.session.get_session_value("_RAW_RESPONSE")
                is_redirect = self.session.get_session_value("_REDIRECT_RESPONSE")
                if self.is_service_controller():
                    self.session.end_user_session()
                if is_raw_response:                    
                    img = open(response,'rb')
                    response = FileResponse(img)
                    return response
                elif is_redirect:
                    return redirect(response)
                else:
                    return HttpResponse(response)
            else:
                response = dict(status="FAILED",err_cd="E006",msg=self.message.get_message("_E003_"),data=valid_resp)
                if self.is_service_controller():
                    self.session.end_user_session()
                return HttpResponse(self.utils.to_json(response))
        else:
            if self.access_ctrl.get_error():
                if self.access_ctrl.get_error() == "SESSION":
                    response = dict(status="FAILED",err_cd="E002",msg=self.message.get_message("_E002_"))
                else:
                    response = dict(status="FAILED",err_cd="E001",msg=self.message.get_message("_E006_"))
            if self.is_service_controller():
                    self.session.end_user_session()
            return HttpResponse(self.utils.to_json(response))

            
    def get_params(self,query_str, request=None, post=None):
        split_params = query_str.split("&")
        name_values = dict()
        for param in split_params:
            name_value = param.split("=")
            if len(name_value) == 2:
                name_values[name_value[0]] = str(urllib.parse.unquote(name_value[1])).strip()
            else:
                name_values[name_value[0]] = ""
                
        if request and (("_action_code" in name_values and name_values["_action_code"] != "PAYU_PAYMENT") or "_action_code" not in name_values):
            json_data = self.utils.parse_json(request)
            for key,value in json_data.items():
                if type(value) == str:
                    name_values[key] = value.strip()
                else:
                    name_values[key] = value

        if post and "_action_code" in name_values and name_values["_action_code"] == "PAYU_PAYMENT":
            for key, value in post.items():
                if type(value) == str:
                    name_values[key] = value.strip()
                else:
                    name_values[key] = value

        return name_values


    def process_files(self,files):
        file_paths = []        
        for key,file in files.items():
            extn = "" if file.name.rfind(".") == -1 else file.name[file.name.rfind("."):]
            file_name = self.utils.get_cur_timestamp_flat() + "_" + AppSecurity.get_random_otp(8) + extn
            fptr = open(self.configs.temp_folder_path + file_name, "wb")
            for file_data in file.chunks():         
                fptr.write(file_data)
            fptr.close()
            file_paths.append({"upload_name": file_name,"name":file.name,"type":file.content_type,"key":key})
            
        return(file_paths)

    #
    # The mehod that invokes the action handler
    #
    def handle_action(self,action_code,params):        
        try:
            self.session.set_session_value("_action_code",action_code)
            self.workflow = self.get_workflow_handler(self.connDB,self.session,self.audit)            
            self.ui_mapping = self.get_ui_mapping()
            
            ret_val = self.get_request_router().handle_action(action_code,params,self.connDB,self.session,self.message,self.ui_mapping,self.workflow)
            
            self.audit.audit_action(action_code,params)

            return ret_val
        
        except Exception as ex:
            raise ex


    def get_service_paths(self):
        service_paths = self.configs.service_urls
        response = dict(status="SUCCESS",err_cd="_E004_",msg=self.message.get_message("_E004_"),data=service_paths)        
        return(response)

    def load_configs(self):
        for config in AppConfigs.application_configs:
            mname = config[:config.rindex(".")]
            cname = config[config.rindex(".")+1:]
            config_class = __import__(mname, fromlist=[cname])
            #self.logger.log_message(inspect.getmembers(config_class))            
    
    #
    # Method to be implemented by the sub classes
    #
    def get_action_overrides(self):
        pass

    #
    # Method to be implemented by the sub classes
    #
    def get_request_router(self):
        pass

    #
    # Method to be implemented by the sub classes
    #
    def get_app_name(self):
        pass

    #
    # Method to be implemented by the sub classes
    #
    def get_message_mapper(self):
        pass

    #
    # Method to be implemented by the sub classes
    #
    def get_validator(self):
        pass

    #
    # Method to be implemented by the sub classes
    #
    def get_auditor(self,session,connDB):
        pass

    #
    # Method to be implemented by the sub classes
    #
    def get_ui_mapping(self):
        pass

    #
    # Method to be implemented by the sub classes
    #
    def get_workflow_handler(self,connDB,session,audit):
        pass

    #
    # Method to be implemented by the sub classes
    #
    def is_service_controller(self):
        return False

#
# Test routine
#
if __name__ == "__main__":
    pass
