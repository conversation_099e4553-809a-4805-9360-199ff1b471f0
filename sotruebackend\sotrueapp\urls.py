from .application.controllers import SotrueAppUserController
from .application.controllers import SotrueAppAdminController
from .application.controllers import SotrueAppPlayController
from django.urls import path
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from django.views import View


class HealthCheckView(View):
    """
    Simple health check endpoint for Render deployment
    Returns 200 OK with basic status information
    """
    def get(self, request):
        return JsonResponse({
            'status': 'healthy',
            'service': 'sotrue-backend',
            'message': 'Service is running'
        })

    def post(self, request):
        return self.get(request)


urlpatterns = [
    path('health', HealthCheckView.as_view(), name='health'),
    path('appservice', csrf_exempt(SotrueAppUserController.as_view()), name='appservice'),
    path('adminservice', csrf_exempt(SotrueAppAdminController.as_view()), name='adminservice'),
    path('playlist', csrf_exempt(SotrueAppPlayController.as_view()), name='playlist')
]