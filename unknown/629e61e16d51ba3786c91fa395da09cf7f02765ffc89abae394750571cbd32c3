from components.AppProcessHandlerBase import AppProcessHandlerBase
from components.AppSecurity import AppSecurity
from components.AppConfigs import AppConfigs
from components.AppEmail import AppEmail
from components.AppRazorPay import AppRazorPay
from components.AppExcel import AppExcel
from .SotrueAppGenericCode import SotrueAppGenericCode
from sotrueapp.application.misc.SotrueAppConfig import SotrueAppConfig

import os

class SotrueAppAdminHandler(AppProcessHandlerBase):
    
    def handle_login(self):
        user_mode = True
        if self.params["user_id"].startswith("##uid##_") and self.params["password"].startswith("##pwd##_"):
            user_mode = False
            qparams = [self.params["user_id"],self.params["password"]]
            credentials = self.connDB.execute_prepared_stmt("sotrueappuser","GET_SESSION_CREDS",qparams)
            if credentials:
                uid = self.params["user_id"][8:]
                pwd = self.params["password"][8:]
                key = credentials[0].key.encode("utf-8")
                self.params["user_id"] = AppSecurity.decrypt_str(uid.encode("utf-8"),key)
                self.params["password"] = AppSecurity.decrypt_str(pwd.encode("utf-8"),key)                
            
        qparams = [self.params["user_id"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_CREDENTIALS",qparams)

        if user_data:
            if user_data[0].status != 'ACTIVE':
                response = self.create_error("AE001")
                return(response)            
            if AppSecurity.check_hash(user_data[0].password,self.params["password"]):                            

                if user_mode:
                    encr_data = AppSecurity.encrypt_str(self.params["user_id"])
                    uid = "##uid##_" + encr_data["value"].decode("utf-8")
                    key = encr_data["key"]
                    encr_data = AppSecurity.encrypt_str(self.params["password"],key)
                    passwd = "##pwd##_" + encr_data["value"].decode("utf-8")

                    qparams = [key.decode("utf-8"),uid,passwd]
                    self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_SESSION_CREDS",qparams,replication=False)            
        
                qparams = [user_data[0].user_seq]
                user_role = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ADMIN_USER_ROLES",qparams)
                response = dict(user_seq=user_data[0].user_seq,                                
                                uid=uid if user_mode else None,
                                pwd=passwd if user_mode else None,
                                user_role=user_role[0].role_name if user_role else "")

                session_data = {"_user_seq":user_data[0].user_seq,"_session_expiry":AppConfigs.session_expiry,
                                "_admin_user":True,"_profile_seq":-1}
                self.session.start_user_session(session_data)
                
                response = self.create_response(response,"AE002")
                return(response)
                                
        response = self.create_error("AE003")
        return(response)


    def send_password_otp(self):
        qparams = [self.params["email"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_CREDENTIALS",qparams)

        if user_data:
            otp = AppSecurity.get_random_otp(8)
            self.logger.log_message("OTP for password reset is : " + str(otp))
            qparams = [otp,user_data[0].user_seq]
            self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_USER_OTP",qparams)
            
            mailer = AppEmail()
            mailer.send_email(self.params["email"],None,"SoTrue Account Password Reset",
                "\nYour OTP to reset the password for your SoTrue account is: " + str(otp) + "\n\nThank You,\nSoTrue Team");
            response = self.create_response(dict(user_seq=user_data[0].user_seq),"AE012")
            return(response)
        
        response = self.create_error("AE011")
        return(response)


    def submit_password_otp(self):
        if not self.session.is_access_key_valid(self.params["_access_key"],-1):
            response = self.create_error("AE009")
            return(response)
    
        qparams = [self.params["user_seq"]]
        otp_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_OTP",qparams)
        if otp_data and otp_data[0].activation_otp == self.params["otp"]:            
            response = self.create_response(dict(),"AE010")
            return(response)
            
        response = self.create_error("AE008")
        return(response)


    def set_new_password(self):
        if not self.session.is_access_key_valid(self.params["_access_key"],-1):
            response = self.create_error("AE009")
            return(response)
        
        qparams = [self.params["user_seq"]]
        otp_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_OTP",qparams)
        if otp_data and otp_data[0].activation_otp == self.params["otp"]:                        
            qparams = [AppSecurity.hash(self.params["password"]),"",self.params["user_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_USER_PASSWORD",qparams)
            response = self.create_response(dict(),"AE007")
            return(response)

        response = self.create_error("AE008")
        return(response)   


    def change_password(self):
        qparams = [AppSecurity.hash(self.params["new_password"]),"",self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_USER_PASSWORD",qparams)

        qparams = [self.params["user_seq"]]
        email_id = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_EMAIL_ID",qparams)
        
        encr_data = AppSecurity.encrypt_str(email_id[0].email_id)
        uid = "##uid##_" + encr_data["value"].decode("utf-8")
        key = encr_data["key"]
        encr_data = AppSecurity.encrypt_str(self.params["new_password"],key)
        passwd = "##pwd##_" + encr_data["value"].decode("utf-8")

        qparams = [key.decode("utf-8"),uid,passwd]
        self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_SESSION_CREDS",qparams)
                    
        response = self.create_response(dict(uid = uid,pwd = passwd),"AE007")
        return(response)


    def get_code_values(self):
        qparams = [self.params["code_type"],"ACTIVE"]
        result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_CODE_VALUES",qparams)
        response = self.utils.convert_tuples_to_dicts(result_set)

        if len(response) != 0:
            response = self.create_response(response,"AE004")
            return(response)
        else:
            response = self.create_error("AE005")
            return(response)


    def get_dashboard_values(self):
        dashboard_data = []
        from_date = None        
        today = self.utils.get_today_date_str()
        to_date = self.utils.retard_date_by(today,1) + " 23:59:59"

        if self.params["date_range"] == "CURRENT_DAY":
            from_date = today + " 00:00:00"
            to_date = today + " 23:59:59"
        elif self.params["date_range"] == "PREVIOUS_DAY":            
            from_date = self.utils.retard_date_by(today,1) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_7_DAYS":
            from_date = self.utils.retard_date_by(today,7) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_15_DAYS":
            from_date = self.utils.retard_date_by(today,15) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_30_DAYS":
            from_date = self.utils.retard_date_by(today,30) + " 00:00:00"            
        elif self.params["date_range"] == "PAST_60_DAYS":
            from_date = self.utils.retard_date_by(today,60) + " 00:00:00"
        elif self.params["date_range"] == "PAST_90_DAYS":
            from_date = self.utils.retard_date_by(today,90) + " 00:00:00"  

        qparams = [from_date,to_date]
        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_NEW_ACCOUNTS",qparams)
        dashboard_data.append({"label":"New Accounts","value":str(result[0].count),"is_currency":"NO"})

        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_DELETED_ACCOUNTS",qparams)
        dashboard_data.append({"label":"Deleted Accounts","value":str(result[0].count),"is_currency":"NO"})

        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_PROFILE_SUBS",qparams)
        dashboard_data.append({"label":"Account Subscribes","value":str(result[0].count),"is_currency":"NO"})

        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_POST_SUBS",qparams)
        dashboard_data.append({"label":"Post Subscribes","value":str(result[0].count),"is_currency":"NO"})

        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_PAYMENTS",qparams)
        dashboard_data.append({"label":"Total Wallet Payin","is_currency":"YES",
                               "value":self.utils.format_currency(int(0 if not result[0].sum else (result[0].sum)/100)),})

        params = [from_date,to_date,'PROFILE']
        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_CHARGES_PROFILE",params)
        dashboard_data.append({"label":"Account Subscribe Payments","is_currency":"YES",
                               "value":self.utils.format_currency(int(0 if not result[0].sum else (result[0].sum)/100))})

        params = [from_date,to_date,'POST']
        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_CHARGES_PROFILE",params)
        dashboard_data.append({"label":"Post Subscribe Payments","is_currency":"YES",
                               "value":self.utils.format_currency(int(0 if not result[0].sum else (result[0].sum)/100))})

        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_PROFILE_UNSUBS",qparams)
        dashboard_data.append({"label":"Account Unsubcribes","value":str(result[0].count),"is_currency":"NO"})

        result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_ACTIVE_USERS",qparams)
        dashboard_data.append({"label":"Active Users","value":str(result[0].count),"is_currency":"NO"})

        #result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_POSTS",qparams)
        #dashboard_data.append({"label":"New Posts","value":str(result[0].count),"is_currency":"NO"})

        #result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_STORIES",qparams)
        #dashboard_data.append({"label":"New Stories","value":str(result[0].count),"is_currency":"NO"})

        #result = self.connDB.execute_prepared_stmt("sotrueappadmin","DASHBOARD_FOLLOWS",qparams)
        #dashboard_data.append({"label":"Free Account Follows","value":str(result[0].count),"is_currency":"NO"})
       
        response = self.create_response(dashboard_data,"AE004")
        return(response)


    def get_gateway_payments(self):
        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",
                   '%' if "transaction_id" not in self.params else '%' + self.params["transaction_id"] + '%',
                   '%' if "order_id" not in self.params else '%' + self.params["order_id"] + '%',
                   '%' if "payment_id" not in self.params else '%' + self.params["payment_id"] + '%',
                   '' if "payment_id" not in self.params else self.params["order_id"],
                   '%' if "display_name" not in self.params else '%' + self.params["display_name"] + '%',
                   '%' if "handle" not in self.params else '%' + self.params["handle"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%',
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "full_name" not in self.params else '%' + self.params["full_name"] + '%',
                   '%' if "pay_status" not in self.params else self.params["pay_status"]]
        subs = {"<MOBILE_NULL>":" "}
        if "mobile" not in self.params:
            subs = {"<MOBILE_NULL>":" OR user_master.mobile_number IS NULL"}

        payment_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_GATEWAY_PAYMENTS_RECEIVED",qparams,subs=subs,limit=self.is_paginated())
        if payment_data:
            payment_data_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_GATEWAY_PAYMENTS_RECEIVED_COUNT",qparams,subs=subs)
            payment_data = self.utils.convert_tuples_to_dicts(payment_data)
            for payment in payment_data:
                payment["amount"]=self.utils.format_currency(payment["amount"]/100)
                payment["response_ts"]=self.utils.format_ts_display(payment["response_ts"])
                payment["init_time"]=self.utils.format_ts_display(payment["init_time"])
            response = self.create_response(payment_data,"AE004",addnl={"_total_rows":payment_data_count[0].count})
            return(response)

        response = self.create_error("AE005")   
        return(response)


    def get_user_reports(self):
        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",
                   '%' if "user_name" not in self.params else '%' + self.params["user_name"] + '%',
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%',
                   '%' if "report_id" not in self.params else '%' + self.params["report_id"] + '%',
                   '%' if "source_id" not in self.params else self.params["source_id"],
                   '%' if "reason_code" not in self.params else self.params["reason_code"],
                   '%' if "status" not in self.params else self.params["status"]
                   ]
        subs = {"<MOBILE_NULL>":" "}
        if "mobile" not in self.params:
            subs = {"<MOBILE_NULL>":" OR user_master.mobile_number IS NULL"}

        report_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_POST_REPORTS",qparams,subs=subs,limit=self.is_paginated())
        if report_data:
            report_data_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_POST_REPORTS_COUNT",qparams,subs=subs)
            report_data = self.utils.convert_tuples_to_dicts(report_data)
            post_seqs = []
            for report in report_data:
                post_seqs.append(report["post_seq"])
            subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}
            reported_users = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PROFILES_FOR_POSTS",None,subs=subs)
            reported_users_indexed = {}
            for user in reported_users:
                reported_users_indexed[user.post_seq] = user.profile_seq

            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            post_seqs = []
            for report in report_data:                
                report["reported_on"]=self.utils.format_ts_display(report["reported_on"])
                report["screen_capture"] = generic_code.create_media_url(SotrueAppConfig.report_path + report["screen_capture"],AppConfigs.s3_others_folder,report["s3_enabled"])                
                report["reported_profile"] = reported_users_indexed[report["post_seq"]] if report["post_seq"] in reported_users_indexed else -1
                
            response = self.create_response(report_data,"AE004",addnl={"_total_rows":report_data_count[0].count})
            return(response)

        response = self.create_error("AE005")   
        return(response)
    

    def get_post_details(self):
        qparams = [self.params['post_seq']]
        post_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_POST_SEQ",qparams)                
                    
        if post_data:                    
            post_data = self.utils.convert_tuples_to_dicts(post_data)            

            qparams = [self.params['profile_seq'],self.params['post_seq']]                    
            post_likes = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_POST_SEQ_LIKE",qparams)
            post_bookmarks = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_POST_SEQ_BOOKMARK",qparams)
            post_subscription = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_POST_SUBSCRIPION",qparams)

            qparams = [self.params['profile_seq']]
            profile_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PROFILE_DETAILS",qparams)

            qparams = [self.params['post_seq']]
            like_counts = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_POST_SEQ_LIKE_COUNT",qparams)
            qparams = [self.params['post_seq'],"POST"]
            comment_counts = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_POST_SEQ_COMMENT_COUNT",qparams)                            

            qparams = [self.utils.get_ts_back_by(self.utils.get_cur_timestamp(),24),post_data[0]["profile_seq"]]
            story_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_STORY_USER_COUNT",qparams)
            
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for row in post_data:                
                row["likes"] = like_counts[0].count
                row["comments"] = comment_counts[0].count
                row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_posts_folder, row['s3_enabled'])
                row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(row["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_posts_folder, row['s3_enabled'])
                row["user_handle"] = profile_data[0].user_handle                
                row["profile_picture"] = profile_data[0].profile_picture if not profile_data[0].profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + profile_data[0].profile_picture,AppConfigs.s3_profiles_folder, profile_data[0].s3_enabled)
                row["display_name"] = profile_data[0].display_name
                row["enable_comment"] = profile_data[0].enable_comment
                row["is_liked"] = 'YES' if post_likes else 'NO'
                row["is_bookmarked"] = 'YES' if post_bookmarks else 'NO'
                row["posted_on"] = self.utils.get_formatted_time_past(row["posted_on"])
                row["story_count"] = story_count[0].count
                row["is_subscribed"] = 'YES' if post_subscription else 'NO'
                row["viewer_fee"] = int(row["viewer_fee"]/100)

            response = self.create_response(post_data,"AE004")
            return(response)
        
        response = self.create_error("AE005")   
        return(response)

    def get_story_details(self):
        qparams = [self.params['story_seq']]
        story_list = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_STORY_USER",qparams)
        
        if story_list:
            story_list = self.utils.convert_tuples_to_dicts(story_list)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for row in story_list:                        
                row["profile_picture"] = row["profile_picture"] if not row["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + row["profile_picture"],AppConfigs.s3_profiles_folder, row["profile_s3"])
                row["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_file"],AppConfigs.s3_stories_folder,row["stories_s3"])
                row["media_cover"] = row["media_cover"] if not row["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_cover"],AppConfigs.s3_stories_folder,row["stories_s3"])
                if row["media_type"] == "VIDEO" and row["video_duration"] == 0:
                    duration = self.utils.get_video_duration(SotrueAppConfig.media_path + str(self.params["profile_seq"]) + "/" + row["media_file"])
                    row["video_duration"] = duration
                    qparams [duration, row["story_seq"]]
                    self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_VIDEO_DURATION_STORY",qparams)

            response = self.create_response(story_list,"UE011")
            return(response)
        
        response = self.create_error("UE012")
        return(response)


    def update_report_status(self):
        qparams = [self.params["report_seq"],self.params["resolution_code"],self.params["comments"],
                   self.session.get_session_value("_user_seq"),self.utils.get_cur_timestamp()]
        resolution_seq = self.connDB.execute_prepared_stmt("sotrueappadmin","INSERT_REPORT_STATUS",qparams)
        qparams = [self.params["status"],self.params['report_seq']]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_REPORT_STATUS",qparams)
        
        if self.params["status"] == "RESOLVED" and self.params["resolution_code"] in {"USER_TRAINING", "USER_ACK", "USER_NO_ACK"}:
            qparams = [self.params["report_seq"]]
            user_details = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_REPORT_USER_DETAILS",qparams)
            params = "p1=" +  user_details[0].reported_by + "&p2=" + self.params["report_seq"]
            params = self.utils.url_encode(params)
            link = "<a href=" + SotrueAppConfig.feedback_url + params + ">Click here to provide feedback</a>"
            mailer = AppEmail()
            mailer.send_email(user_details[0].email_id,None,"How did we do?",
                "Hello,\n\nWe recently had the opportunity to assist you and hope the resolution met your expectations.\n\nPlease take a moment to provide your feedback on our services by clicking the link below:\n\n" + link + "\n\nThank you for helping us improve!\n\nBest,\nTeam SoTrue\n")

        response = self.create_response(dict(resolution_seq=resolution_seq),"AE006")
        return(response)


    def get_report_updates(self):
        qparams = [self.params["report_seq"]]
        resolution_list = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_REPORT_RESOLUTION",qparams)
        resolution_list = self.utils.convert_tuples_to_dicts(resolution_list)
        if resolution_list:
            for rec in resolution_list:
                rec["resolved_on"] = self.utils.format_ts_display(rec["resolved_on"])
            response = self.create_response(resolution_list,"AE004")
            return(response)
        response = self.create_error("AE005")   
        return(response)


    def get_posts(self):
        like_char = "%"
        if "exact_search" in self.params and self.params["exact_search"] == "YES":
            like_char = ""

        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",
                   '%' if "user_name" not in self.params else like_char + self.params["user_name"] + like_char,
                   '%' if "user_handle" not in self.params else like_char + self.params["user_handle"] + like_char,
                   '%' if "email" not in self.params else like_char + self.params["email"] + like_char,
                   '%' if "mobile" not in self.params else like_char + self.params["mobile"] + like_char,
                   self.params["block_status"],
                   '%' if "review_status" not in self.params else self.params["review_status"],
                   '%' if "type" not in self.params else self.params['type']]
        subs = {"<MOBILE_NULL>":" "}
        if "mobile" not in self.params:
            subs = {"<MOBILE_NULL>":" OR user_master.mobile_number IS NULL"}

        posts_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_POSTS",qparams,subs=subs,limit=self.is_paginated())
        if posts_data: 
            posts_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_POSTS_COUNT",qparams,subs=subs)
            posts_data = self.utils.convert_tuples_to_dicts(posts_data)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for post in posts_data:                
                post["posted_on"]=self.utils.format_ts_display(post["posted_on"])
                post["blocked_on"]= "-" if not post["blocked_on"] else self.utils.format_ts_display(post["blocked_on"])
                post["expire_on"]='-' if not post["expire_on"] else self.utils.format_ts_display(post["expire_on"])
                post["viewer_fee"]='0.00' if not post["viewer_fee"] else self.utils.format_currency((post["viewer_fee"])/100)
                post["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(post["profile_seq"]) + "/" + post["media_file"],AppConfigs.s3_posts_folder, post["s3_enabled"])
                post["media_cover"] = post["media_cover"] if not post["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(post["profile_seq"]) + "/" + post["media_cover"],AppConfigs.s3_posts_folder,post["s3_enabled"])
                
            response = self.create_response(posts_data,"AE004",addnl={"_total_rows":posts_count[0].count})
            return(response)

        response = self.create_error("AE005")   
        return(response)


    def get_stories(self):
        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",
                   '%' if "user_name" not in self.params else '%' + self.params["user_name"] + '%',
                   '%' if "user_handle" not in self.params else '%' + self.params["user_handle"] + '%',
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%',
                   self.params["block_status"],
                   '%' if "review_status" not in self.params else self.params["review_status"]
                   ]
        subs = {"<MOBILE_NULL>":" "}
        if "mobile" not in self.params:
            subs = {"<MOBILE_NULL>":" OR user_master.mobile_number IS NULL"}

        posts_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_STORIES",qparams,subs=subs,limit=self.is_paginated())
        if posts_data: 
            posts_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_STORIES_COUNT",qparams,subs=subs)
            posts_data = self.utils.convert_tuples_to_dicts(posts_data)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for post in posts_data:                
                post["posted_on"]=self.utils.format_ts_display(post["posted_on"])
                post["blocked_on"]= "-" if not post["blocked_on"] else self.utils.format_ts_display(post["blocked_on"])
                post["expire_on"]='-' if not post["expire_on"] else self.utils.format_ts_display(post["expire_on"])               
                post["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path + str(post["profile_seq"]) + "/" + post["media_file"],AppConfigs.s3_stories_folder,post["s3_enabled"])
                post["media_cover"] = post["media_cover"] if not post["media_cover"] else generic_code.create_media_url(SotrueAppConfig.media_path + str(post["profile_seq"]) + "/" + post["media_cover"],AppConfigs.s3_stories_folder,post["s3_enabled"])
                
            response = self.create_response(posts_data,"AE004",addnl={"_total_rows":posts_count[0].count})
            return(response)

        response = self.create_error("AE005")   
        return(response)

    def set_post_block_status(self):
        if self.params["block_status"] == "BLOCKED":
            qparams = [self.params["block_status"], 
                   "" if not "block_comments" in self.params else self.params["block_comments"], 
                   self.session.get_session_value("_user_seq"),self.utils.get_cur_timestamp(),
                   self.params["post_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappadmin","SET_POST_BLOCK_STATUS",qparams)

            qparams = [self.params["post_seq"]]
            email_id = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_EMAIL_POST",qparams)
            mailer = AppEmail()
            mailer.send_email(email_id[0].email_id,None,"Sotrue Content Notification",
                "Hello,\n\nWe regret to inform you that your content with the caption '" + email_id[0].post_comments + "' does not comply with our community guidelines. Please review our terms and conditions for further details and clarity.\n\nThank you for your understanding.\n\nBest regards,\nTeam SoTrue",                
                AppConfigs.email_sender);
        else:
             qparams = [self.params["block_status"], self.params["post_seq"]]
             self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_POST_BLOCK_STATUS",qparams)        
        response = self.create_response(dict(),"AE006")
        return(response)

    def set_story_block_status(self):
        if self.params["block_status"] == "BLOCKED":
            qparams = [self.params["block_status"], 
                   "" if not "block_comments" in self.params else self.params["block_comments"], 
                   self.session.get_session_value("_user_seq"),self.utils.get_cur_timestamp(),
                   self.params["story_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappadmin","SET_STORY_BLOCK_STATUS",qparams)

            qparams = [self.params["story_seq"]]
            email_id = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_EMAIL_STORY",qparams)
            mailer = AppEmail()
            mailer.send_email(email_id[0].email_id,None,"Sotrue Content Notification",
                "\nOh no! Unfortunately your content posted with the caption" + email_id[0].story_comments + " violates our community guidelines, please refer to the terms and conditions for some more clarity! \n\nYour Biggest Fans,\nTeam SoTrue",
                AppConfigs.email_sender);
        else:
             qparams = [self.params["block_status"], self.params["story_seq"]]
             self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_STORY_BLOCK_STATUS",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)

    def set_post_review_status(self):
        qparams = ['YES', self.params["post_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_POST_REVIEW_STATUS",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)

    def set_story_review_status(self):
        qparams = ['YES', self.params["story_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_STORY_REVIEW_STATUS",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)

    def get_profiles(self):
        response = self.get_profiles_data()
        if type(response) is dict:
            response = self.create_response(response["response"],"AE004",addnl=response["addnl"])
            return response
        else:
            return response        

    def get_profiles_data(self,full_report=False):
        like_char = "%"
        if "exact_search" in self.params and self.params["exact_search"] == "YES":
            like_char = ""

        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",
                   self.params["block_status"],
                   #'%' if "verified_status" not in self.params else self.params["verified_status"],
                   '%' if "user_handle" not in self.params else like_char + self.params["user_handle"] + like_char,
                   '%' if "display_name" not in self.params else like_char + self.params["user_name"] + like_char,
                   '%' if "user_name" not in self.params else like_char + self.params["user_name"] +like_char,                   
                   '%' if "email" not in self.params else like_char + self.params["email"] + like_char,
                   '%' if "mobile" not in self.params else like_char + self.params["mobile"] + like_char,                   
                   ]
        subs = {"<MOBILE_NULL>":" ","<TYPE_QUERY>": ""}
        if "mobile" not in self.params:
            subs["<MOBILE_NULL>"] = " OR user_master.mobile_number IS NULL"

        if "type" in self.params:
            subs["<TYPE_QUERY>"] = " AND user_master.is_dummy='" + self.params["type"] + "'"
        else:
            subs["<TYPE_QUERY>"] = " AND (user_master.is_dummy IS NULL OR user_master.is_dummy='NORMAL') "

        if full_report:
            profiles_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_PROFILES",qparams,subs=subs)
        else:
            profiles_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_PROFILES",qparams,subs=subs,limit=self.is_paginated())
        if profiles_data:                         
            profiles_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_PROFILES_COUNT",qparams,subs=subs)
            
            qparams = ["DELETE_OPTION","ACTIVE"]
            delete_reasons = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_CODE_VALUES",qparams)
            delete_reasons_indexed = {}
            for rec in delete_reasons:
                delete_reasons_indexed[rec.config_key] = rec.display_value

            profiles_data = self.utils.convert_tuples_to_dicts(profiles_data)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for profile in profiles_data:                
                profile["created_on"]=self.utils.format_ts_display(profile["created_on"])
                profile["blocked_on"]= "-" if not profile["blocked_on"] else self.utils.format_ts_display(profile["blocked_on"])                
                profile["paid_account_fee"]='0.00' if not profile["paid_account_fee"] else self.utils.format_currency((profile["paid_account_fee"])/100)
                profile["profile_picture"] = profile["profile_picture"] if not profile["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + profile["profile_picture"],AppConfigs.s3_profiles_folder,profile["s3_enabled"])                              
                mode = ""
                if profile["delete_reason"]:
                    reasons = profile["delete_reason"].split(",")
                    reason_str = ""                   
                    for r in reasons:
                        if not mode and (r=="DELETE" or r=="INACTIVE"):
                            mode = r
                            continue
                        if reason_str:
                            reason_str += ", "
                        if r in delete_reasons_indexed:                                                        
                            reason_str += delete_reasons_indexed[r]
                        else:
                            reason_str += r
                    profile["delete_reason"] = reason_str
                if mode:
                    profile["delete_action"] = "Deactivated" if mode=="INACTIVE" else "Deleted"
                else:
                    profile["delete_action"] = "Deactivated"
                
            totals = {}
            totals["_total_rows"] = profiles_count[0].count       
            return({"response":profiles_data,"addnl":totals})            

        response = self.create_error("AE005")   
        return(response)


    def get_profiles_xls(self):
        response = self.get_profiles_data(True)
        if type(response) is not dict:            
            return response

        excel_data = []
        for rec in response["response"]:            
            row = [rec["user_handle"],rec["created_on"],rec["type"],rec["display_name"],rec["paid_account_fee"],rec["blocked_reason"],
                   rec["blocked_on"],rec["full_name"],rec["email_id"],rec["mobile_number"],rec["status"],rec["is_verified"],rec["bank_account"],
                   rec["ifsc_code"],rec["country"],rec["state"],rec["gstin"],rec["razorpay_acc"]]
            excel_data.append(row)        

        extn = ".xlsx"
        file_name = "Profiles_Report_" + self.utils.get_time_for_file()+"_"+str(self.session.get_session_value("_user_seq"))+extn

        heading = "Profiles Report"
        summary = ["Created On: " + self.utils.get_current_ts_display(),
                   "From Date: " + self.utils.format_date_display(self.params["from_date"])  if "from_date" in self.params else "From Date: Minimum",
                   "To Date: " + self.utils.format_date_display(self.params["to_date"]) if "to_date" in self.params else "To Date: Maximum"]

        writer = AppExcel()
        col_headings = ["User Handle","Created On","Type","Display Name","Paid Account Fee","Blocked Reason",
                        "Blocked On","Full Name","Email Id","Mobile Number","Status","Is Verified","Bank Account",
                        "IFSC Code","Country","State","GST Number","Razorpay Account"]
        writer.write_to_excel(os.path.join(SotrueAppConfig.report_temp_path,file_name),excel_data,col_headings,heading,summary)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)        
        #file_name = os.path.join(SotrueAppConfig.report_temp_url,file_name)
        #file_name = SotrueAppConfig.base_url + file_name
        response = [{"file_path":generic_code.create_media_url(SotrueAppConfig.report_temp_path + file_name,None,"NO")}]        
        response = self.create_response(response,"AE004")
        return(response)

    def set_profile_block_status(self):
        if self.params["block_status"] == "BLOCKED":
            qparams = [self.params["block_status"], 
                   "" if not "block_comments" in self.params else self.params["block_comments"], 
                   self.session.get_session_value("_user_seq"),self.utils.get_cur_timestamp(),
                   self.params["profile_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappadmin","SET_PROFILE_BLOCK_STATUS",qparams)
        else:
             qparams = [self.params["block_status"], self.params["profile_seq"]]
             self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_PROFILE_BLOCK_STATUS",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)


    def get_verifications(self):
        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",                   
                   self.params["verified_status"],
                   '%' if "user_handle" not in self.params else '%' + self.params["user_handle"] + '%',
                   '%' if "display_name" not in self.params else '%' + self.params["user_name"] + '%',
                   '%' if "user_name" not in self.params else '%' + self.params["user_name"] + '%',                   
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%',   
                   '%' if "type" not in self.params else '%' + self.params["type"] + '%',
                   ]
        subs = {"<MOBILE_NULL>":" "}
        if "mobile" not in self.params:
            subs = {"<MOBILE_NULL>":" OR user_master.mobile_number IS NULL"}

        verification_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_VERIFICATION_STATUS",qparams,subs=subs,limit=self.is_paginated())
        if verification_data: 
            verification_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_VERIFICATION_STATUS_COUNT",qparams,subs=subs)
            verification_data = self.utils.convert_tuples_to_dicts(verification_data)
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            for verify in verification_data:                
                verify["submitted_on"]=self.utils.format_ts_display(verify["submitted_on"])
                verify["verified_on"]= "-" if not verify["verified_on"] else self.utils.format_ts_display(verify["verified_on"])                                
                verify["verification_picture"] = verify["verification_picture"] if not verify["verification_picture"] else generic_code.create_media_url(SotrueAppConfig.verification_docs + verify["verification_picture"],AppConfigs.s3_accounts_folder,verify["s3_enabled"])                              
                verify["document_image"] = verify["document_image"] if not verify["document_image"] else generic_code.create_media_url(SotrueAppConfig.verification_docs + verify["document_image"],AppConfigs.s3_accounts_folder,verify["s3_enabled"])
                verify["combined_image"] = verify["combined_image"] if not verify["combined_image"] else generic_code.create_media_url(SotrueAppConfig.verification_docs + verify["combined_image"],AppConfigs.s3_accounts_folder,verify["s3_enabled"])
                
            response = self.create_response(verification_data,"AE004",addnl={"_total_rows":verification_count[0].count})
            return(response)

        response = self.create_error("AE005")   
        return(response)


    def update_verification_status(self):
        qparams = [self.params["profile_seq"]]
        latest = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_LATEST_VERIFICATION",qparams)
        if latest and latest[0].verification_seq != self.params["verification_seq"]:
            response = self.create_error("AE013")   
            return(response)

        qparams = [self.params["verified_status"],self.params["verification_comment"],self.utils.get_cur_timestamp(),
                   self.session.get_session_value("_user_seq"),self.params["verification_seq"],self.params["profile_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_VERIFICATION_STATUS",qparams)

        qparams = ["YES" if self.params["verified_status"] == "VERIFIED" else "NO", self.params["profile_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_VERIFICATION_STATUS_PROFILE",qparams)
        
        qparams = ['INACTIVE',self.params["profile_seq"],'VERIFY_FAILED']
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_ALERT_STATUS",qparams)

        if self.params["verified_status"] != "PENDING":
            qparams = [self.params["profile_seq"],
                   "Congratulations! You have been verified. Please check out our FAQ's for some additional clarity on SoTrue"  if self.params["verified_status"] == "VERIFIED" else "Verification Request Rejected: " + self.params["verification_comment"],
                   "VERIFY_SUCCESS" if self.params["verified_status"] == "VERIFIED" else "VERIFY_FAILED",
                   "NO","ACTIVE",
                   "YES"  if self.params["verified_status"] == "VERIFIED" else "NO"]
            alert_seq = self.connDB.execute_prepared_stmt("sotrueappadmin","INSERT_PROFILE_ALERT",qparams)
        
        subs = {'<PROFILE_LIST>':str(self.params["profile_seq"])}
        profile_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_DETAILS",None,subs=subs)    
        mailer = AppEmail()
        if self.params["verified_status"] == "VERIFIED":            
            mailer.send_email(profile_data[0].email_id,None,"SoTrue Account Verification",
                "Congratulations!\n\nYour account has been successfully verified. For additional information and clarity on SoTrue, please refer to our FAQs.\n\nBest,\nTeam SoTrue\n")
        elif self.params["verified_status"] == "REJECTED":
            mailer.send_email(profile_data[0].email_id,None,"SoTrue Account Verification",
                              "Hello,\n\nUnfortunately, we were unable to approve your verification request. Please address the issues listed below and resubmit your request.\n\nIssue Details: " + self.params["verification_comment"] + "\n\nThank you for your attention to this matter.\n\nBest,\nTeam SoTrue\n")

        response = self.create_response(dict(),"AE006")
        return(response)


    def get_payouts_report(self):
        response = self.get_payouts_report_data()
        if type(response) is dict:
            response = self.create_response(response["response"],"FE007",addnl=response["addnl"])
            return response
        else:
            return response


    def get_payouts_report_data(self,full_report=False):
        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",                   
                   self.params["is_settled"],
                   '%' if "user_handle" not in self.params else '%' + self.params["user_handle"] + '%',
                   '%' if "display_name" not in self.params else '%' + self.params["user_name"] + '%',
                   '%' if "user_name" not in self.params else '%' + self.params["user_name"] + '%',                   
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%',                   
                   ]
        if self.params["type"] == "POST":
            if full_report:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_POST",qparams)
            else:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_POST",qparams,limit=self.is_paginated())
            result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_POST_COUNT",qparams)
        elif self.params["type"] == "PROFILE":
            if full_report:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_PROFILE",qparams)
            else:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_PROFILE",qparams,limit=self.is_paginated())
            result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_PROFILE_COUNT",qparams)
        elif self.params["type"] == "REFERRAL":
            if full_report:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_REFERRAL",qparams)
            else:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_REFERRAL",qparams,limit=self.is_paginated())
            result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_REFERRAL_COUNT",qparams)

        if not result_set:
            response = self.create_error("AE005")
            return(response)

        result_set = self.utils.convert_tuples_to_dicts(result_set)
        totals = dict(total_gst = 0, total_tax = 0, total_comission = 0, total_payable = 0, total_value = 0,
                      total_inward =0, total_outward = 0, total_tcs = 0)
        for rec in result_set:
            rec["paid_on"] = self.utils.format_ts_display(rec["paid_on"])
            
            paid_amount = rec["paid_amount"]/100
            totals['total_value'] += paid_amount
                      
            totals["total_tax"] += rec["tds"]/100
            rec["income_tax"] = self.utils.format_currency(rec["tds"] / 100)                        
            rec["base_amt"] = self.utils.format_currency(rec["paid_amount"]/100)

            if not "comission" in rec:
                rec["comission"] = 0
            totals["total_comission"] += rec["comission"]/100
            rec["comission"]  = self.utils.format_currency(rec["comission"]/100)

            totals["total_payable"] += rec["payable"]/100
            rec["payable"] = self.utils.format_currency(rec["payable"]/100)

            totals["total_gst"] += rec["gst"]/100
            rec["gst"]  = self.utils.format_currency(rec["gst"]/100)                                                

            if not "inward_charges" in rec:
                rec["inward_charges"] = 0
            rec["inward_charges"] =  self.utils.format_currency(rec["inward_charges"]/100)
            rec["outward_charges"] =  self.utils.format_currency(rec["outward_charges"]/100)
            
            if not "tcs" in rec:
                rec["tcs"] = 0
            rec["tcs"] =  self.utils.format_currency(rec["tcs"]/100)
            
            if not "gst_comission" in rec:
                rec["gst_comission"] = 0
            rec["gst_comission"] =  self.utils.format_currency(rec["gst_comission"]/100) if rec["gst_comission"] else self.utils.format_currency(0)

        totals["total_payable"] = self.utils.format_currency(totals["total_payable"])
        totals["total_gst"] = self.utils.format_currency(totals["total_gst"])
        totals["total_comission"] = self.utils.format_currency(totals["total_comission"])
        totals["total_tax"] = self.utils.format_currency(totals["total_tax"])
        totals["total_value"] = self.utils.format_currency(totals["total_value"])

        totals["_total_rows"] = result_count[0].count       
        return({"response":result_set,"addnl":totals})


    def get_payouts_xls_report(self):
        response = self.get_payouts_report_data(True)
        if type(response) is not dict:            
            return response

        excel_data = []
        for rec in response["response"]:            
            row = [rec["user_seq"],rec["full_name"],rec["user_handle"],rec["email_id"],rec["mobile_number"],rec["paid_on"],
                   rec["base_amt"], rec["gst"],rec["comission"],rec["gst_comission"],rec["income_tax"],
                   rec["tcs"],rec["inward_charges"],rec["outward_charges"],rec["payable"]]
            excel_data.append(row)
        row = ["","","","","","Total",response["addnl"]["total_value"]]
        excel_data.append(row)

        extn = ".xlsx"
        file_name = "Payouts_Report_" + self.utils.get_time_for_file()+"_"+str(self.session.get_session_value("_user_seq"))+extn

        heading = "Payouts Report"
        summary = ["Created On: " + self.utils.get_current_ts_display(),
                   "From Date: " + self.utils.format_date_display(self.params["from_date"])  if "from_date" in self.params else "From Date: Minimum",
                   "To Date: " + self.utils.format_date_display(self.params["to_date"]) if "to_date" in self.params else "To Date: Maximum",
                   "Type: " + self.params["type"]]

        writer = AppExcel()
        col_headings = ["User Id","Full Name","Handle","Email ID","Mobile Number","Payment Received On","Base Amount",
                        "GST","Commission","GST Commission","TDS","TCS","Inward Charges","Outward Charges","Payable"]
        writer.write_to_excel(os.path.join(SotrueAppConfig.report_temp_path,file_name),excel_data,col_headings,heading,summary)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)        
        #file_name = os.path.join(SotrueAppConfig.report_temp_url,file_name)
        #file_name = SotrueAppConfig.base_url + file_name
        response = [{"file_path":generic_code.create_media_url(SotrueAppConfig.report_temp_path + file_name,None,"NO")}]        
        response = self.create_response(response,"AE004")
        return(response)


    def settle_payout(self):
        pay_out_seqs = self.utils.parse_json(self.params["payout_seqs"])
        for seq in pay_out_seqs:
            qparams = ['YES',self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                       self.params["comments"],seq["charge_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappadmin","SETTLE_PAYMENT",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)

    def settle_payout_consolidated(self):
        payout_data =  self.utils.parse_json(self.params["payout_data"])
        for payout in payout_data:
            qparams = [payout["profile_seq"]]
            user_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PROFILE_DETAILS",qparams)
            if user_data and user_data[0].razorpay_acc:
                qparams = [payout["profile_seq"], self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                       payout["amount"], "TEMP"]
                payout_seq = self.connDB.execute_prepared_stmt("sotrueappadmin","INSERT_PAYOUT",qparams)            
                razorpay = AppRazorPay()
                payment_id = razorpay.make_payment(user_data[0].razorpay_acc,payout["amount"],payout_seq)            
                if payment_id:
                    qparams = ["INITIATED",payment_id,payout_seq]
                    self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_PAYOUT",qparams)

                    qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                                '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",
                                payout["profile_seq"]]
                    post_payouts = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENTS_POST",qparams)
                    profile_payouts = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENTS_PROFILE",qparams)
                    referral_payouts = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENTS_REFERRAL",qparams)
                    for rec in post_payouts:
                       qparams = ['YES',self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                                        self.params["comments"] if "comments" in self.params else "",
                                        payout_seq, rec.charge_seq]
                       self.connDB.execute_prepared_stmt("sotrueappadmin","SETTLE_CHARGE",qparams)
                    for rec in profile_payouts:
                       qparams = ['YES',self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                                        self.params["comments"] if "comments" in self.params else "",
                                        payout_seq, rec.charge_seq]
                       self.connDB.execute_prepared_stmt("sotrueappadmin","SETTLE_CHARGE",qparams)
                    for rec in referral_payouts:
                       qparams = ['YES',self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                                        self.params["comments"] if "comments" in self.params else "",
                                        payout_seq, rec.earning_seq]
                       self.connDB.execute_prepared_stmt("sotrueappadmin","SETTLE_REFERRAL",qparams)
                else:                    
                    qparams = ["FAILED","",payout_seq]
                    self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_PAYOUT",qparams)

        response = self.create_response(dict(),"AE006")
        return(response)


    def settle_payout_referral(self):
        pay_out_seqs = self.utils.parse_json(self.params["payout_seqs"])
        for seq in pay_out_seqs:
            qparams = ['YES',self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                       self.params["comments"],seq["charge_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappadmin","SETTLE_PAYMENT_REFERRAL",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)


    def get_payouts_grouped(self):
        response = self.get_payouts_grouped_data()
        if type(response) is dict:
            response = self.create_response(response["response"],"FE007",addnl=response["addnl"])
            return response
        else:
            return response

    def get_payouts_grouped_data(self,full_report=False):
        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",                   
                   self.params["is_settled"],
                   '%' if "user_handle" not in self.params else '%' + self.params["user_handle"] + '%',
                   '%' if "display_name" not in self.params else '%' + self.params["user_name"] + '%',
                   '%' if "user_name" not in self.params else '%' + self.params["user_name"] + '%',                   
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%',                   
                   ]
        if self.params["type"] == "POST":
            if full_report:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_POST_GROUPED",qparams)
            else:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_POST_GROUPED",qparams,limit=self.is_paginated())
            result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_POST_GROUPED_COUNT",qparams)
            result_set = self.utils.convert_tuples_to_dicts(result_set)
            result_count = self.utils.convert_tuples_to_dicts(result_count)
        elif self.params["type"] == "PROFILE":
            if full_report:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_PROFILE_GROUPED",qparams)
            else:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_PROFILE_GROUPED",qparams,limit=self.is_paginated())
            result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_PROFILE_GROUPED_COUNT",qparams)
            result_set = self.utils.convert_tuples_to_dicts(result_set)
            result_count = self.utils.convert_tuples_to_dicts(result_count)
        elif self.params["type"] == "REFERRAL":
            if full_report:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_REFERRAL_SETTLEMENT_PROFILE_GROUPED",qparams)
            else:
                result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_REFERRAL_SETTLEMENT_PROFILE_GROUPED",qparams,limit=self.is_paginated())
            result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_REFERRAL_SETTLEMENT_PROFILE_GROUPED_COUNT",qparams)
            result_set = self.utils.convert_tuples_to_dicts(result_set)
            result_count = self.utils.convert_tuples_to_dicts(result_count)
        elif self.params["type"] == "ALL":
            result_set_post = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_POST_GROUPED",qparams)
            result_set_profile = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYMENT_SETTLEMENT_PROFILE_GROUPED",qparams)
            result_set_referral = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_REFERRAL_SETTLEMENT_PROFILE_GROUPED",qparams)
            profile_seqs = {}
            result_set_post_indexed = {}
            result_set_profile_indexed = {}
            result_set_referral_indexed = {}
            for rec in result_set_post:               
                profile_seqs[rec.profile_seq] = "POST"
                result_set_post_indexed[rec.profile_seq] = rec

            for rec in result_set_profile:
                if rec.profile_seq not in profile_seqs:
                    profile_seqs[rec.profile_seq] = "PROFILE"
                else:
                    profile_seqs[rec.profile_seq] = "POST_PROFILE"
                result_set_profile_indexed[rec.profile_seq] = rec

            for rec in result_set_referral:
                if rec.profile_seq not in profile_seqs:
                    profile_seqs[rec.profile_seq] = "REFERRAL"
                else:
                    if profile_seqs[rec.profile_seq] == "POST_PROFILE":
                        profile_seqs[rec.profile_seq] = "ALL"
                    elif profile_seqs[rec.profile_seq] == "POST":
                        profile_seqs[rec.profile_seq] = "POST_REFERRAL"
                    elif profile_seqs[rec.profile_seq] == "PROFILE":
                        profile_seqs[rec.profile_seq] = "PROFILE_REFERRAL"
                result_set_referral_indexed[rec.profile_seq] = rec

            profile_seqs = dict(sorted(profile_seqs.items()))

            result_set = []
            result_count = []
            for key,value in profile_seqs.items():
                if value == "POST":
                    result_set.append({"paid_amount":result_set_post_indexed[key].paid_amount,
                                       "gst":result_set_post_indexed[key].gst,
                                       "outward_charges":result_set_post_indexed[key].outward_charges,
                                       "tds":result_set_post_indexed[key].tds,
                                       "payable":result_set_post_indexed[key].payable,
                                       "comission":result_set_post_indexed[key].comission,
                                       "inward_charges":result_set_post_indexed[key].inward_charges,
                                       "gst_comission":result_set_post_indexed[key].gst_comission,
                                       "tcs":result_set_post_indexed[key].tcs,
                                            "profile_seq":result_set_post_indexed[key].profile_seq})
                elif value == "PROFILE":
                    result_set.append({"paid_amount":result_set_profile_indexed[key].paid_amount,
                                       "gst":result_set_profile_indexed[key].gst,
                                       "outward_charges":result_set_profile_indexed[key].outward_charges,
                                       "tds":result_set_profile_indexed[key].tds,
                                       "payable":result_set_profile_indexed[key].payable,
                                       "comission":result_set_profile_indexed[key].comission,
                                       "inward_charges":result_set_profile_indexed[key].inward_charges,
                                       "gst_comission":result_set_profile_indexed[key].gst_comission,
                                       "tcs":result_set_profile_indexed[key].tcs,
                                            "profile_seq":result_set_profile_indexed[key].profile_seq})
                elif value == "REFERRAL":
                    result_set.append({"paid_amount":result_set_referral_indexed[key].paid_amount,
                                       "gst":result_set_referral_indexed[key].gst,
                                       "outward_charges":result_set_referral_indexed[key].outward_charges,
                                       "tds":result_set_referral_indexed[key].tds,
                                       "payable":result_set_referral_indexed[key].payable,
                                            "profile_seq":result_set_referral_indexed[key].profile_seq})
                elif value == "POST_PROFILE":
                    result_set.append({"paid_amount":result_set_post_indexed[key].paid_amount + result_set_profile_indexed[key].paid_amount,
                                       "gst":result_set_post_indexed[key].gst + result_set_profile_indexed[key].gst,
                                       "outward_charges":result_set_post_indexed[key].outward_charges + result_set_profile_indexed[key].gst,
                                       "tds":result_set_post_indexed[key].tds + result_set_profile_indexed[key].tds,
                                       "payable":result_set_post_indexed[key].payable + result_set_profile_indexed[key].payable,
                                       "comission":result_set_post_indexed[key].comission + result_set_profile_indexed[key].comission,
                                       "inward_charges":result_set_post_indexed[key].inward_charges + result_set_profile_indexed[key].inward_charges,
                                       "gst_comission":result_set_post_indexed[key].gst_comission + result_set_profile_indexed[key].gst_comission,
                                       "tcs":result_set_post_indexed[key].tcs + result_set_profile_indexed[key].tcs,
                                            "profile_seq":result_set_post_indexed[key].profile_seq})
                elif value == "POST_REFERRAL":
                    result_set.append({"paid_amount":result_set_post_indexed[key].paid_amount + result_set_referral_indexed[key].paid_amount,
                                       "gst":result_set_post_indexed[key].gst + result_set_referral_indexed[key].gst,
                                       "outward_charges":result_set_post_indexed[key].outward_charges + result_set_referral_indexed[key].gst,
                                       "tds":result_set_post_indexed[key].tds + result_set_referral_indexed[key].tds,
                                       "payable":result_set_post_indexed[key].payable + result_set_referral_indexed[key].payable,
                                       "comission":result_set_post_indexed[key].comission,
                                       "inward_charges":result_set_post_indexed[key].inward_charges,
                                       "gst_comission":result_set_post_indexed[key].gst_comission,
                                       "tcs":result_set_post_indexed[key].tcs,
                                            "profile_seq":result_set_post_indexed[key].profile_seq})
                elif value == "PROFILE_REFERRAL":
                    result_set.append({"paid_amount":result_set_profile_indexed[key].paid_amount + result_set_referral_indexed[key].paid_amount,
                                       "gst":result_set_profile_indexed[key].gst + result_set_referral_indexed[key].gst,
                                       "outward_charges":result_set_profile_indexed[key].outward_charges + result_set_referral_indexed[key].gst,
                                       "tds":result_set_profile_indexed[key].tds + result_set_referral_indexed[key].tds,
                                       "payable":result_set_profile_indexed[key].payable + result_set_referral_indexed[key].payable,
                                       "comission":result_set_profile_indexed[key].comission,
                                       "inward_charges":result_set_profile_indexed[key].inward_charges,
                                       "gst_comission":result_set_profile_indexed[key].gst_comission,
                                       "tcs":result_set_profile_indexed[key].tcs,
                                            "profile_seq":result_set_profile_indexed[key].profile_seq})
                elif value == "ALL":
                    result_set.append({"paid_amount":result_set_post_indexed[key].paid_amount + result_set_profile_indexed[key].paid_amount + result_set_referral_indexed[key].paid_amount,
                                       "gst":result_set_post_indexed[key].gst + result_set_profile_indexed[key].gst + result_set_referral_indexed[key].gst,
                                       "outward_charges":result_set_post_indexed[key].outward_charges + result_set_profile_indexed[key].outward_charges + result_set_referral_indexed[key].outward_charges,
                                       "tds":result_set_post_indexed[key].tds + result_set_profile_indexed[key].tds + result_set_referral_indexed[key].tds,
                                       "payable":result_set_post_indexed[key].payable + result_set_profile_indexed[key].payable + result_set_referral_indexed[key].payable,
                                       "comission":result_set_post_indexed[key].comission + result_set_profile_indexed[key].comission,
                                       "inward_charges":result_set_post_indexed[key].inward_charges + result_set_profile_indexed[key].inward_charges,
                                       "gst_comission":result_set_post_indexed[key].gst_comission + result_set_profile_indexed[key].gst_comission,
                                       "tcs":result_set_post_indexed[key].tcs + result_set_profile_indexed[key].tcs,
                                            "profile_seq":result_set_post_indexed[key].profile_seq})                   

            result_count.append({"count":len(profile_seqs)})

            if not full_report:            
                page = self.is_paginated()
                if page:
                    if page[0] < len(result_set):
                        result_set = result_set[page[0]:page[0]+page[1]]
                    else:
                        result_set = []
       
        if not result_set:
            response = self.create_error("AE005")
            return(response)
        
        totals = dict(total_gst = 0, total_tax = 0, total_comission = 0, total_payable = 0, total_value = 0,
                      total_inward =0, total_outward = 0, total_tcs = 0)
        profile_list = []
        for rec in result_set:
            profile_list.append(rec["profile_seq"])
        subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_list,",")}
        gst_pan_details = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_GST_PAN_DETAILS",None,subs=subs)
        gst_details_indexed = {}
        pan_details_indexed = {}
        for rec in gst_pan_details:
            if rec.pan:
                pan_details_indexed[rec.profile_seq] = rec.pan
            if rec.gstin:
                gst_details_indexed[rec.profile_seq] = rec.gstin

        for rec in result_set:            
            paid_amount = rec["paid_amount"]/100
            totals['total_value'] += paid_amount            

            if not "comission" in rec:
                rec["comission"] = 0
            totals["total_comission"] += rec["comission"]
            rec["comission"] = self.utils.format_currency(rec["comission"]/100)          

            totals["total_payable"] += rec["payable"]
            rec["payable_paisa"] = rec["payable"]
            rec["payable"] = self.utils.format_currency(rec["payable"]/100)            

            totals["total_gst"] += rec["gst"]
            rec["gst"]  = self.utils.format_currency(rec["gst"]/100)                        

            totals["total_tax"] += rec["tds"]
            rec["income_tax"]  = self.utils.format_currency(rec["tds"]/100)

            rec["paid_amount"] = self.utils.format_currency(rec["paid_amount"]/100)
            rec["base_amt"] = rec["paid_amount"]

            if not "inward_charges" in rec:
                rec["inward_charges"] = 0
            rec["inward_charges"] = self.utils.format_currency(rec["inward_charges"]/100)

            rec["outward_charges"] = self.utils.format_currency(0)           

            if not "gst_comission" in rec:
                rec["gst_comission"] = 0
            rec["gst_comission"] = self.utils.format_currency(rec["gst_comission"]/100) if rec["gst_comission"] else self.utils.format_currency(0)

            if not "tcs" in rec:
                rec["tcs"] = 0
            rec["tcs"] = self.utils.format_currency(rec["tcs"]/100)

        subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_list,",")}
        user_list = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_DETAILS",None,subs=subs)
        user_list_indexed = {}
        for user in user_list:
            user_list_indexed[user.profile_seq] = user

        for rec in result_set:
            if rec["profile_seq"] in user_list_indexed:
                rec["user_handle"] = user_list_indexed[rec["profile_seq"]].user_handle
                rec["user_seq"] = user_list_indexed[rec["profile_seq"]].user_seq
                rec["full_name"] = user_list_indexed[rec["profile_seq"]].full_name
                rec["email_id"] = user_list_indexed[rec["profile_seq"]].email_id
                rec["mobile_number"] = user_list_indexed[rec["profile_seq"]].mobile_number
                rec["country"] = user_list_indexed[rec["profile_seq"]].country
                rec["state"] = user_list_indexed[rec["profile_seq"]].state
                rec["razorpay_acc"] = user_list_indexed[rec["profile_seq"]].razorpay_acc
            else:
                rec["user_handle"] = "-"
                rec["user_seq"] = "-"
                rec["full_name"] = "-"
                rec["email_id"] = "-"
                rec["mobile_number"] = "-"
                rec["country"] = "-"
                rec["state"] = "-"
                rec["razorpay_acc"] = "-"

        totals["total_payable"] = self.utils.format_currency(totals["total_payable"])
        totals["total_gst"] = self.utils.format_currency(totals["total_gst"])
        totals["total_comission"] = self.utils.format_currency(totals["total_comission"])
        totals["total_tax"] = self.utils.format_currency(totals["total_tax"])
        totals["total_value"] = self.utils.format_currency(totals["total_value"])

        totals["_total_rows"] = result_count[0]["count"]       
        return({"response":result_set,"addnl":totals})


    def get_payouts_xls_grouped(self):
        response = self.get_payouts_grouped_data(True)
        if type(response) is not dict:            
            return response

        excel_data = []
        for rec in response["response"]:            
            row = [rec["user_seq"],rec["full_name"],rec["user_handle"],rec["email_id"],rec["mobile_number"],rec["country"],rec["state"],
                   rec["paid_amount"],rec["gst"],rec["comission"],rec["gst_comission"],rec["income_tax"],
                   rec["tcs"],rec["inward_charges"],rec["outward_charges"],rec["payable"]]
            excel_data.append(row)
        row = ["","","","","Total",response["addnl"]["total_value"]]
        excel_data.append(row)

        extn = ".xlsx"
        file_name = "Payouts_Grouped_" + self.utils.get_time_for_file()+"_"+str(self.session.get_session_value("_user_seq"))+extn

        heading = "Payouts Report"
        summary = ["Created On: " + self.utils.get_current_ts_display(),
                   "From Date: " + self.utils.format_date_display(self.params["from_date"])  if "from_date" in self.params else "From Date: Minimum",
                   "To Date: " + self.utils.format_date_display(self.params["to_date"]) if "to_date" in self.params else "To Date: Maximum",
                   "Type: " + self.params["type"]]

        writer = AppExcel()
        col_headings = ["User Id","Full Name","Handle","Email ID","Mobile Number","Country","State","Base Amount",
                        "GST","Commission","GST Commission","TDS","TCS","Inward Charges","Outward Charges","Payable"]
        writer.write_to_excel(os.path.join(SotrueAppConfig.report_temp_path,file_name),excel_data,col_headings,heading,summary)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)             
        #file_name = os.path.join(SotrueAppConfig.report_temp_url,file_name)
        #file_name = SotrueAppConfig.base_url + file_name
        response = [{"file_path":generic_code.create_media_url(SotrueAppConfig.report_temp_path + file_name,None,"NO")}]        
        response = self.create_response(response,"AE004")
        return(response)


    def get_user_comissions(self):
        response = self.get_user_comissions_data()
        if type(response) is dict:
            response = self.create_response(response["response"],"FE007",addnl=response["addnl"])
            return response
        else:
            return response


    def get_user_comissions_data(self,full_report=False):
        qparams = ['%' if "user_name" not in self.params else '%' + self.params["user_name"] + '%',                   
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%']
        if full_report:
           result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_COMISSIONS",qparams)
        else:
           result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_COMISSIONS",qparams,limit=self.is_paginated())

        if not result_set:
            response = self.create_error("AE005")
            return(response)

        result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_COMISSIONS_COUNT",qparams)

        result_set = self.utils.convert_tuples_to_dicts(result_set)
        for rec in result_set:
            rec["comission_pc"] = self.utils.format_decimal(rec["comission_pc"] / 100)

        return({"response":result_set,"addnl":{"_total_rows":result_count[0].count}})


    def get_user_comissions_xls(self):
        response = self.get_user_comissions_data(True)
        if type(response) is not dict:            
            return response

        excel_data = []
        for rec in response["response"]:            
            row = [rec["user_seq"],rec["full_name"],rec["email_id"],rec["mobile_number"],rec["comission_pc"]]
            excel_data.append(row)       

        extn = ".xlsx"
        file_name = "Commission_" + self.utils.get_time_for_file()+"_"+str(self.session.get_session_value("_user_seq"))+extn

        heading = "Commissions Report"
        summary = ["Created On: " + self.utils.get_current_ts_display()]

        writer = AppExcel()
        col_headings = ["User Id","Full Name","Email ID","Mobile Number","Commission %"]
        writer.write_to_excel(os.path.join(SotrueAppConfig.report_temp_path,file_name),excel_data,col_headings,heading,summary)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)             
        #file_name = os.path.join(SotrueAppConfig.report_temp_url,file_name)
        #file_name = SotrueAppConfig.base_url + file_name
        response = [{"file_path":generic_code.create_media_url(SotrueAppConfig.report_temp_path + file_name,None,"NO")}]        
        response = self.create_response(response,"AE004")
        return(response)


    def get_comission_history(self):
        qparams = [self.params["user_seq"]]
        history_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_COMISSION_DATA",qparams)

        if not history_data:
            response = self.create_error("AE005")
            return(response)

        history_data = self.utils.convert_tuples_to_dicts(history_data)
        for rec in history_data:
            rec["updated_on"] = self.utils.format_ts_display(rec["updated_on"])
            rec["percentage"] = self.utils.format_decimal(rec["percentage"]/100)

        response = self.create_response(history_data,"AE004")
        return(response)


    def update_comission(self):
        qparams = [self.params["user_seq"]]
        comission_data = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_COMISSION_SEQ",qparams)
        if comission_data[0].comission_pc == int(float(self.params["comission_pc"])*100):
            response = self.create_error("AE014")
            return(response)

        qparams = [self.params["user_seq"],int(float(self.params["comission_pc"])*100),self.session.get_session_value("_user_seq"),
                   self.utils.get_cur_timestamp(),self.params["comments"]]
        comission_seq = self.connDB.execute_prepared_stmt("sotrueappadmin","INSERT_COMISSION",qparams)

        qparams = [int(float(self.params["comission_pc"])*100),self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_COMISSION",qparams)

        response = self.create_response(dict(comission_seq=comission_seq),"AE006")
        return(response)

    def update_data(self):
        try:            
            qparams = self.utils.parse_json(self.params["params"])
            self.logger.log_message(qparams)
            self.logger.log_message(self.params)
            self.connDB.execute_update_query(self.params["query"],qparams,self.params["db_key"],
                                                  self.params["db_schema"])
            response = self.create_response(dict(),"AE015")
            return(response)
        except Exception:
            response = self.create_error("AE018")
            return(response)


    def replicate_file(self):              
        if self.params["uploads"] is not None:
            file_path = self.configs.temp_folder_path + self.params["uploads"][0]["upload_name"]
            target_path = SotrueAppConfig.media_base_path + self.params["file_path"]
            if not self.utils.folder_exists(target_path):
                self.utils.create_folder(target_path)            
            self.utils.copy_files(file_path,target_path + self.params["uploads"][0]["name"])
            self.utils.delete_file(file_path)

            response = self.create_response(dict(),"AE016")
            return(response)
        
        response = self.create_error("AE017")
        return(response)


    def check_remote_duplicate_user(self):
        user_data = None
        if self.params["handle"]:
            qparams = [self.params["handle"]]
            user_data = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_DUPLICATE_HANDLE",qparams)
        elif self.params["email"]:
            qparams = [self.params["email"]]
            user_data = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_DUPLICATE_EMAIL",qparams)
        if not user_data:
            response = self.create_error("AE019")
        else:
            response = self.create_response(dict(),"AE020")
        return(response);

    def get_payout_requests(self):
        response = self.get_payout_requests_data()
        if type(response) is dict:
            response = self.create_response(response["response"],"AE004",addnl=response["addnl"])
            return response
        else:
            return response


    def get_payout_requests_data(self,full_report=False):
        qparams = ['%' if "req_month" not in self.params else self.params["req_month"],                   
                   'SETTLED' if self.params["is_settled"] == 'YES' else 'PENDING',
                   '%' if "user_handle" not in self.params else '%' + self.params["user_handle"] + '%',
                   '%' if "display_name" not in self.params else '%' + self.params["display_name"] + '%',
                   '%' if "user_name" not in self.params else '%' + self.params["user_name"] + '%',                   
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%',                   
                   ]
        if full_report:
            result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYOUT_REQUESTS",qparams)
        else:
            result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYOUT_REQUESTS",qparams,limit=self.is_paginated())
        result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PAYOUT_REQUESTS_COUNT",qparams)

        if not result_set:
            response = self.create_error("AE005")
            return(response)

        result_set = self.utils.convert_tuples_to_dicts(result_set)
        for rec in result_set:
            rec["total_payout"] = self.utils.format_currency(rec["total_payout"]/100)
            rec["referral_payout"] = self.utils.format_currency(rec["referral_payout"]/100)
            rec["subs_payout"] = self.utils.format_currency(rec["subs_payout"]/100)
            rec["requested_month"] = self.utils.convert_month_name(rec["requested_month"])

        totals = {}
        totals["_total_rows"] = result_count[0].count       
        return({"response":result_set,"addnl":totals})


    def get_payout_requests_xls(self):
        response = self.get_payout_requests_data(True)
        if type(response) is not dict:            
            return response

        excel_data = []
        for rec in response["response"]:            
            row = [rec["payout_seq"],rec["full_name"],rec["user_handle"],rec["email_id"],rec["mobile_number"],rec["requested_month"],
                   rec["total_payout"],rec["referral_payout"],rec["subs_payout"],rec["gstin"]]
            excel_data.append(row)        

        extn = ".xlsx"
        file_name = "Payout_Requests_Report_" + self.utils.get_time_for_file()+"_"+str(self.session.get_session_value("_user_seq"))+extn

        heading = "Payout Requests Report"
        summary = ["Created On: " + self.utils.get_current_ts_display(),
                   "Payout Month: " + "-" if "req_month" not in self.params else self.utils.convert_month_name(self.params["req_month"])]

        writer = AppExcel()
        col_headings = ["Request ID","Full Name","Handle","Email ID","Mobile Number","Requested Month","Total Amount","Referral Amount","Subscription Amount","GSTN"]
        writer.write_to_excel(os.path.join(SotrueAppConfig.report_temp_path,file_name),excel_data,col_headings,heading,summary)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)        
        #file_name = os.path.join(SotrueAppConfig.report_temp_url,file_name)
        #file_name = SotrueAppConfig.base_url + file_name
        response = [{"file_path":generic_code.create_media_url(SotrueAppConfig.report_temp_path + file_name,None,"NO")}]        
        response = self.create_response(response,"AE004")
        return(response)


    def settle_payout_request(self):
        qparams = ["SETTLED",self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                   self.params["comments"], self.params["payout_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_PAYOUT_REQUEST_STATUS",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)

    


    def update_user_gstn(self):
        qparams = [self.params["gstn"],self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_USER_GSTN",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)

    def update_user_razorpay_acc(self):
        qparams = [self.params["razorpay_acc"],self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_USER_RAZORPAY_ACC",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)

    def get_feedback_ratings(self):
        response = self.get_feedback_ratings_data()
        if type(response) is dict:
            response = self.create_response(response["response"],"AE004",addnl=response["addnl"])
            return response
        else:
            return response

    def get_feedback_ratings_data(self,full_report=False):
        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",
                    '%' if "user_handle" not in self.params else '%' + self.params["user_handle"] + '%',
                   '%' if "display_name" not in self.params else '%' + self.params["display_name"] + '%',
                   '%' if "full_name" not in self.params else '%' + self.params["full_name"] + '%',                   
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%'
                   ]
        if full_report:
            result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_FEEDBACK",qparams)
        else:
            result_set = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_FEEDBACK",qparams,limit=self.is_paginated())
        result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_FEEDBACK_COUNT",qparams)

        if not result_set:
            response = self.create_error("AE005")
            return(response)

        result_set = self.utils.convert_tuples_to_dicts(result_set)
        for rec in result_set:           
            rec["feedback_on"] = self.utils.format_ts_display(rec["feedback_on"])

        totals = {}
        totals["_total_rows"] = result_count[0].count       
        return({"response":result_set,"addnl":totals})


    def get_feedback_ratings_xls(self):
        response = self.get_feedback_ratings_data(True)
        if type(response) is not dict:            
            return response

        excel_data = []
        for rec in response["response"]:            
            row = [rec["feedback_seq"],rec["full_name"],rec["user_handle"],rec["email_id"],rec["mobile_number"],rec["display_name"],
                   rec["feedback_on"],rec["feedback_rating"],rec["feedback_comment"],rec["report_id"]]
            excel_data.append(row)        

        extn = ".xlsx"
        file_name = "User_Feedback_Report_" + self.utils.get_time_for_file()+"_"+str(self.session.get_session_value("_user_seq"))+extn

        heading = "User Feedback Report"
        summary = ["Created On: " + self.utils.get_current_ts_display(),
                   "From Date: " + self.utils.format_date_display(self.params["from_date"])  if "from_date" in self.params else "From Date: Minimum",
                   "To Date: " + self.utils.format_date_display(self.params["to_date"]) if "to_date" in self.params else "To Date: Maximum"]

        writer = AppExcel()
        col_headings = ["Feedback ID","Full Name","Handle","Email ID","Mobile Number","Display Name",
                        "Feedback On","Feedback Rating","Feedback Comment","Report Id"]
        writer.write_to_excel(os.path.join(SotrueAppConfig.report_temp_path,file_name),excel_data,col_headings,heading,summary)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)        
        #file_name = os.path.join(SotrueAppConfig.report_temp_url,file_name)
        #file_name = SotrueAppConfig.base_url + file_name
        response = [{"file_path":generic_code.create_media_url(SotrueAppConfig.report_temp_path + file_name,None,"NO")}]        
        response = self.create_response(response,"AE004")
        return(response)


    def get_gateway_payouts(self):
        response = self.get_gateway_payouts_data()
        if type(response) is dict:
            response = self.create_response(response["response"],"AE004",addnl=response["addnl"])
            return response
        else:
            return response


    def get_gateway_payouts_data(self, full_report=False):
        qparams = ['2021-01-01 00:00:00' if "from_date" not in self.params else self.params["from_date"] + " 00:00:00",
                   '9999-12-31 23:59:59' if "to_date" not in self.params else self.params["to_date"] + " 23:59:59",
                    '%' if "user_handle" not in self.params else '%' + self.params["user_handle"] + '%',
                   '%' if "display_name" not in self.params else '%' + self.params["display_name"] + '%',
                   '%' if "full_name" not in self.params else '%' + self.params["full_name"] + '%',                   
                   '%' if "email" not in self.params else '%' + self.params["email"] + '%',
                   '%' if "mobile" not in self.params else '%' + self.params["mobile"] + '%'
                   ]
        if full_report:
            payouts = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_GATEWAY_PAYOUTS",qparams)
        else:
            payouts = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_GATEWAY_PAYOUTS",qparams,limit=self.is_paginated())
        result_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_GATEWAY_PAYOUTS_COUNT",qparams)

        if not payouts:
            response = self.create_error("AE005")
            return(response)

        result_set = self.utils.convert_tuples_to_dicts(payouts)
        for rec in result_set:           
            rec["initiated_on"] = self.utils.format_ts_display(rec["initiated_on"])
            rec["amount"] = self.utils.format_currency(rec["amount"]/100)

        return({"response":result_set,"addnl":{"_total_rows":result_count[0].count}})    
            

    def get_gateway_payouts_xls(self):
        response = self.get_gateway_payouts_data(True)
        if type(response) is not dict:            
            return response

        excel_data = []
        for rec in response["response"]:            
            row = [rec["payout_seq"],rec["full_name"],rec["user_handle"],rec["email_id"],rec["mobile_number"],rec["display_name"],
                   rec["initiated_on"],rec["razorpay_pay_id"],rec["amount"]]
            excel_data.append(row)        

        extn = ".xlsx"
        file_name = "Gateway_Payout_Report_" + self.utils.get_time_for_file()+"_"+str(self.session.get_session_value("_user_seq"))+extn

        heading = "Gateway Payout Report"
        summary = ["Created On: " + self.utils.get_current_ts_display(),
                   "From Date: " + self.utils.format_date_display(self.params["from_date"])  if "from_date" in self.params else "From Date: Minimum",
                   "To Date: " + self.utils.format_date_display(self.params["to_date"]) if "to_date" in self.params else "To Date: Maximum"]

        writer = AppExcel()
        col_headings = ["Payout ID","Full Name","Handle","Email ID","Mobile Number","Display Name",
                        "Initiated On","Razorpay Pay Id","Amount"]
        writer.write_to_excel(os.path.join(SotrueAppConfig.report_temp_path,file_name),excel_data,col_headings,heading,summary)

        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)        
        response = [{"file_path":generic_code.create_media_url(SotrueAppConfig.report_temp_path + file_name,None,"NO")}]        
        response = self.create_response(response,"AE004")
        return(response)


    def create_gateway_account(self):
        qparams = [self.params["user_seq"]]
        email_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_OTP",qparams)
        account_data = self.connDB.execute_prepared_stmt("sotrueappuser","GET_ACCOUNT_DETAILS",qparams)

        # Create the razorpay accounts
        contact_id = None
        account_id = None
        if account_data[0].account_holder and email_data[0].email_id and email_data[0].mobile_number and \
            account_data[0].ifsc_code and account_data[0].bank_account:
            razorpay = AppRazorPay()
            contact_id = razorpay.create_contact(account_data[0].account_holder,email_data[0].email_id,
                                             email_data[0].mobile_number,self.params["user_seq"])            
            if contact_id:
                qparams = [contact_id,account_data[0].profile_seq]
                self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_RAZORPAY_CONTACT",qparams)
                account_id = razorpay.create_account(contact_id,account_data[0].account_holder,account_data[0].ifsc_code,
                                             account_data[0].bank_account)       
                if account_id:
                    qparams = [account_id,account_data[0].profile_seq]
                    self.connDB.execute_prepared_stmt("sotrueappuser","UPDATE_RAZORPAY_ACCOUNT",qparams)

        if contact_id and account_id:
            response = self.create_response(dict(),"AE021")
            return(response)
        else:
            response = self.create_error("AE022")
            return(response)


    def delete_account(self):
        qparams = [self.params["profile_seq"]]
        user_seq = self.connDB.execute_prepared_stmt("sotrueappuser","GET_USER_USER_SEQ",qparams)

        qparams = ["DELETED",user_seq[0].user_seq]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_DELETE_STATUS",qparams)

        response = self.create_response(dict(),"AE024")
        return(response)


    def add_landing_profile(self):
        qparams = [self.params["profile_seq"],self.session.get_session_value("_user_seq"),
                   self.utils.get_cur_timestamp(),'ACTIVE']
        landing_seq = self.connDB.execute_prepared_stmt("sotrueappadmin","ADD_LANDING_PROFILE",qparams)
        response = self.create_response(dict(landing_seq=landing_seq),"AE006")
        return(response)


    def delete_landing_profile(self):
        qparams = ['INACTIVE',self.session.get_session_value("_user_seq"),
                   self.utils.get_cur_timestamp(),self.params["landing_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_LANDING_PROFILE",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)


    def get_landing_profiles(self):
        landing_profiles = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_LANDING_PROFILES",None)
        if not landing_profiles:
            response = self.create_error("AE005")
            return(response)
        user_seqs = []
        for rec in landing_profiles:
            if rec.added_by not in user_seqs:
                user_seqs.append(rec.added_by)
        subs = {"<USER_SEQ_LIST>":self.utils.convert_to_delim_str(user_seqs,",")}
        user_details = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ADMIN_USER_DETAILS",None,subs=subs)
        user_details_indexed = {}
        for rec in user_details:
            user_details_indexed[rec.user_seq] = rec.full_name

        landing_profiles = self.utils.convert_tuples_to_dicts(landing_profiles)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for rec in landing_profiles:                        
            rec["added_by_name"] = user_details_indexed[rec["added_by"]] if rec["added_by"] in user_details_indexed else "-"
            rec["profile_picture"] = rec["profile_picture"] if not rec["profile_picture"] else generic_code.create_media_url(SotrueAppConfig.profile_path + rec["profile_picture"],AppConfigs.s3_profiles_folder, rec["s3_enabled"])
        response = self.create_response(landing_profiles,"AE004")
        return(response)


    def edit_profile(self):
        qparams = [self.params["email"],self.params["user_seq"]]
        user_details = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_DUP_USER",qparams)
        if user_details:
            response = self.create_error("AE025")
            return(response)

        qparams = [self.params["full_name"],self.params["email"],self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_USER_DETAILS",qparams)

        response = self.create_response(dict(),"AE006")
        return(response)


    def set_profile_password(self):
        qparams = [AppSecurity.hash(self.params["user_password"]), self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_USER_PASSWORD_ADMIN",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)


    def get_engagements(self):
        qparams = ['%']
        if "status" in self.params:
            qparams = [self.params['status']]

        engagements = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ENGAGEMENTS",qparams,limit=self.is_paginated())
        if not engagements:
            response = self.create_error("AE005")
            return(response)
        post_seqs = []
        profile_seqs = []
        for rec in engagements:
            if rec.type == "LIKE" or rec.type == "VIEW":
                post_seqs.append(rec.target_seq)
            else:
                profile_seqs.append(rec.target_seq)
        profiles = []
        if profile_seqs:
            subs = {"<PROFILE_LIST>":self.utils.convert_to_delim_str(profile_seqs,",")}
            profiles = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ENGAGEMENT_PROFILES",None,subs=subs)
        profiles_indexed = {}
        for rec in profiles:
            profiles_indexed[rec.profile_seq] = rec

        posts = []
        if post_seqs:
            subs = {"<POST_LIST>":self.utils.convert_to_delim_str(post_seqs,",")}
            posts = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ENGAGEMENT_POSTS",None,subs=subs)
        posts_indexed = {}
        for rec in posts:
            posts_indexed[rec.post_seq] = rec

        engagements = self.utils.convert_tuples_to_dicts(engagements)
        for rec in engagements:
            rec["profile_name"] = "-"
            rec["user_handle"] = "-"
            if (rec["type"] == "LIKE" or rec["type"] == "VIEW") and rec["target_seq"] in posts_indexed:
                rec["user_name"] = posts_indexed[rec["target_seq"]].full_name
                rec["user_handle"] = posts_indexed[rec["target_seq"]].user_handle            
            elif rec["type"] == "FOLLOW" and rec["target_seq"] in profiles_indexed:
                rec["user_name"] = profiles_indexed[rec["target_seq"]].full_name
                rec["user_handle"] = profiles_indexed[rec["target_seq"]].user_handle 
            rec["submitted_on"] = self.utils.format_ts_display(str(rec["submitted_on"]))
        eng_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ENGAGEMENT_COUNT",None)

        response = self.create_response(engagements,"AE004",addnl={"_total_rows":eng_count[0].count})
        return(response)


    def create_engagement(self):
        if self.params["count"] >= 2500 or self.params["count"] <= 0:
            response = self.create_error("AE026")
            return(response)

        if int(self.params["duration"]) < 30:
            response = self.create_error("AE028")
            return(response)

        if int(self.params["count"])/int(self.params["duration"]) >= 30:
            response = self.create_error("AE027")
            return(response)

        target_seqs = self.utils.parse_json(self.params["target_seqs"])
        for target_seq in target_seqs:
            qparams = [target_seq]
            free_users = None
            if self.params["action_type"] == "LIKE":            
                free_users = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_FREE_DUMMY_USERS_LIKE",qparams)
            elif self.params["action_type"] == "FOLLOW":
                free_users = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_FREE_DUMMY_USERS_FOLLOW",qparams)
            elif self.params["action_type"] == "VIEW":            
                free_users = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_FREE_DUMMY_USERS_VIEW",qparams)

            #qparams = [self.params["target_seq"],self.params["action_type"]]
            #check_dup = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_DUPLICATE_ACTION",qparams)
            #if check_dup:
            #    response = self.create_error("AE029")
            #    return(response)
            if free_users[0].count < int(self.params["count"]):
                response = self.create_error("AE030")
                return(response)

        for target_seq in target_seqs:            
            qparams = [self.session.get_session_value("_user_seq"),self.utils.get_cur_timestamp(),
                       self.params["duration"],self.params["action_type"],target_seq,
                       self.params["comments"],"ACTIVE",self.params["count"]]
            action_seq = self.connDB.execute_prepared_stmt("sotrueappadmin","INSERT_ENGAGEMENT",qparams)

        response = self.create_response(dict(action_seq=action_seq),"AE006")
        return(response)


    def terminate_engagement(self):
        qparams = ['INACTIVE',self.params["action_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_ENGAGEMENT",qparams)

        response = self.create_response(dict(),"AE006")
        return(response)


    def submit_bg_notification(self):
        day = self.params["start_time"].split(" ")[0]
        qparams = [day+" 00:00:00", day+" 23:59:59"]
        job_count = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_BG_NOTIFICATION",qparams)
        if len(job_count) == 2:
            response = self.create_error("AE031")
            return(response)
        elif len(job_count) == 1:
            if(self.params["start_time"] >= str(job_count[0].scheduled_start)):
                diff = self.utils.date_diff_hrs(str(job_count[0].scheduled_start),self.params["start_time"])
            else:
                diff = self.utils.date_diff_hrs(self.params["start_time"],str(job_count[0].scheduled_start))
            if(diff<6):
                response = self.create_error("AE031")
                return(response)

        qparams = [self.utils.get_cur_timestamp(),self.session.get_session_value("_user_seq"),
                   self.params["start_time"],self.params["msg_title"],self.params["msg_body"],
                   'ACTIVE']
        notification_seq = self.connDB.execute_prepared_stmt("sotrueappadmin","INSERT_BG_NOTIFICATION",qparams)
        response = self.create_response(dict(notification_seq=notification_seq),"AE006")
        return(response)


    def get_all_bg_notifications(self):
        notifications = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_BG_NOTIFICATIONS",None,limit=self.is_paginated())
        if not notifications:
            response = self.create_error("AE005")
            return(response)
        count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_ALL_BG_NOTIFICATIONS_COUNT",None)
        result_set = self.utils.convert_tuples_to_dicts(notifications)
        for rec in result_set:
            rec["created_on"] = self.utils.format_ts_display(str(rec["created_on"]))
            rec["scheduled_start"] = self.utils.format_ts_display(str(rec["scheduled_start"]))
        response = self.create_response(result_set,"AE004",addnl={"_total_rows":count[0].count})
        return(response)


    def get_bg_notification(self):
        qparams = [self.params['notification_seq']]
        notification = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_BG_NOTIFICATION",qparams)
        result_set = self.utils.convert_tuples_to_dicts(notification)
        response = self.create_response(result_set,"AE004")
        return(response)


    def update_bg_notification(self):
        qparams = [self.params['notification_seq']]
        notification = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_BG_NOTIFICATION",qparams)
        if notification[0].status != 'ACTIVE':
            response = self.create_error("AE033")
            return(response)
        day = self.params["start_time"].split(" ")[0]
        qparams = [day+" 00:00:00", day+" 23:59:59", self.params['notification_seq']]
        job_count = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_BG_NOTIFICATION_SEQ",qparams)
        if len(job_count) == 2:
            response = self.create_error("AE031")
            return(response)
        elif len(job_count) == 1:
            if(self.params["start_time"] >= str(job_count[0].scheduled_start)):
                diff = self.utils.date_diff_hrs(str(job_count[0].scheduled_start),self.params["start_time"])
            else:
                diff = self.utils.date_diff_hrs(self.params["start_time"],str(job_count[0].scheduled_start))
            if(diff<6):
                response = self.create_error("AE031")
                return(response)

        qparams = [self.params["start_time"],self.params["msg_title"],self.params["msg_body"],
                   self.params["notification_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_BG_NOTIFICATION",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)


    def cancel_bg_notification(self):
        qparams = [self.params['notification_seq']]
        notification = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_BG_NOTIFICATION",qparams)
        if notification[0].status != 'ACTIVE':
            response = self.create_error("AE033")
            return(response)
        qparams = ['INACTIVE',self.params['notification_seq']]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_BG_NOTIFICATION_STATUS",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)


    def approve_playlist_prog(self):
        qparams = ["YES",self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_USER_PLAYLIST_STATUS",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)


    def add_playlist_user(self):
        qparams = [self.params["user_name"],'ACTIVE',self.params["user_seq"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_DUP_PLAYLIST_USER",qparams)
        if user_data:
            response = self.create_error("AE036")
            return(response)

        qparams = [self.params["user_name"],AppSecurity.hash(self.params["user_password"]),self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_PLAYLIST_USER",qparams)
        response = self.create_response(dict(),"AE035")
        return(response)


    def update_play_list_user(self):
        qparams = [self.params["user_name"],'ACTIVE',self.params["user_seq"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappadmin","CHECK_DUP_PLAYLIST_USER",qparams)
        if user_data:
            response = self.create_error("AE036")
            return(response)

        qparams = [self.params["user_name"],AppSecurity.hash(self.params["user_password"]),self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_PLAYLIST_USER",qparams)
        response = self.create_response(dict(),"AE035")
        return(response)


    def delete_playlist_user(self):
        qparams = [None,None,self.params["user_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_PLAYLIST_USER",qparams)
        response = self.create_response(dict(),"AE034")
        return(response)


    def get_pending_shows(self):
        qparams =['ACTIVE',self.params["status"]]
        shows = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PENDING_SHOWS",qparams,limit=self.is_paginated())
        if not shows:
            response = self.create_error("AE005")
            return(response)

        shows = self.utils.convert_tuples_to_dicts(shows)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)

        for show in shows:            
            show["logo_file"] = show["logo_file"] if not show["logo_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["logo_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
            show["thumb_file"] = show["thumb_file"] if not show["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["thumb_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
            show["preview_file"] = show["preview_file"] if not show["preview_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show["preview_file"],AppConfigs.s3_posts_folder,show["s3_enabled"])
            show["expiry_date"] = self.utils.format_date_display(str(show["expiry_date"])) if show["expiry_date"] else ""
            show["scheduled_on"] = self.utils.format_ts_display(str(show["scheduled_on"])) if show["scheduled_on"] else ""

        show_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PENDING_SHOWS_COUNT",qparams)
        response = self.create_response(shows,"AE004",addnl={"_total_rows":show_count[0].show_count})
        return(response)


    def update_show_status(self):
        qparams = [self.params["status"],
                   self.params["comments"] if "comments" in self.params else None,self.params["show_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_SHOW_STATUS",qparams)

        qparams = [self.params["show_seq"]]
        user_details = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_FOR_SHOW",qparams)
        mailer = AppEmail()
        if self.params["status"] == "REJECTED":
            mailer.send_email(user_details[0].email_id,None,"SoTrue Playlist Submission Rejected",
                "Hello,\n\nWe regret to inform you that your submission for the SoTrue Playlist has been rejected. The reason for rejection is as follows:\n\n" + (self.params["comments"] if "comments" in self.params else "-") + "\n\nThank you for your understanding.\n\nBest,\nTeam SoTrue\n");
        else:
            mailer.send_email(user_details[0].email_id,None,"SoTrue Playlist Submission Approved",
                "Hey there!\n\nWe are pleased to inform you that your submission for the SoTrue Playlist has been approved.\n\nThank you for your contribution!\n\nBest,\nTeam SoTrue\n");
        
        response = self.create_response(dict(),"AE006")
        return(response)


    def get_pending_episodes(self):
        qparams =['ACTIVE',self.params["status"]]
        episode_details = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PENDING_EPISODES",qparams,limit=self.is_paginated())
        if not episode_details:
            response = self.create_error("AE005")
            return(response)     
        
        episode_details = self.utils.convert_tuples_to_dicts(episode_details)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)

        for episode in episode_details:                    
            episode["thumb_file"] = episode["thumb_file"] if not episode["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + episode["thumb_file"],AppConfigs.s3_posts_folder,episode["s3_enabled"])        
            episode["preview_file"] = episode["preview_file"] if not episode["preview_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + episode["preview_file"],AppConfigs.s3_posts_folder,episode["s3_enabled"])        
            episode["expiry_date"] = self.utils.format_date_display(str(episode["expiry_date"])) if episode["expiry_date"] else ""
            episode["schedule"] = self.utils.format_ts_display(str(episode["schedule"])) if episode["schedule"] else ""
                
        episode_count = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_PENDING_EPISODES_COUNT",qparams)
        response = self.create_response(episode_details,"AE004",addnl={"_total_rows":episode_count[0].episode_count})
        return(response)


    def get_clips_episode(self):
        qparams = ['ACTIVE',self.params["episode_seq"]]
        clips = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_CLIPS_FOR_EPISODE",qparams)
        if not clips:
            response = self.create_error("AE005")
            return(response) 
        clips = self.utils.convert_tuples_to_dicts(clips)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)    
        for clip in clips:
            clip["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path +  clip["media_file"],AppConfigs.s3_posts_folder,clip["s3_enabled"])

        response = self.create_response(clips,"AE004")
        return(response)


    def update_episode_status(self):        
        qparams = [self.params["status"],self.params["comments"] if "comments" in self.params else None,
                    self.params["episode_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappadmin","UPDATE_EPISODE_STATUS",qparams)

        if self.params["status"] == "REJECTED":
            qparams = [self.params["episode_seq"]]
            user_details = self.connDB.execute_prepared_stmt("sotrueappadmin","GET_USER_FOR_EPISODE",qparams)
            mailer = AppEmail()
            mailer.send_email(user_details[0].email_id,None,"SoTrue Playlist Submission rejected",
                "\nYour submission for Sotrue Playlist has been rejected. The rejection reason is as follows:\n\n" + (self.params["comments"] if "comments" in self.params else "-") + "\n\nThank You,\nSoTrue Team");

        response = self.create_response(dict(),"AE006")
        return(response)