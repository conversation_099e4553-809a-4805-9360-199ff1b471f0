from components.AppControllerBase import A<PERSON><PERSON>ontrollerBase
from .SotrueAppUserRequestRouter import Sotrue<PERSON>ppUserRequestRouter
from sotrueapp.application.misc.SotrueAppUserMessageMapper import SotrueAppUserMessageMapper
from sotrueapp.application.misc.SotrueAppUserValidator import SotrueAppUserValidator
from sotrueapp.application.misc.SotrueAppUserAuditRules import SotrueAppUserAuditRules
from sotrueapp.application.misc.SotrueAppUIMapping import SotrueAppUIMapping
from sotrueapp.application.misc.SotrueAppWorkflow import SotrueAppWorkflow

class SotrueAppUserController(AppControllerBase):

    def get_action_overrides(self):
        return(['APP_LOGIN','GMAIL_LOGIN','APPLE_LOGIN','APP_REGISTER','SUBMIT_OTP','SUBMIT_OTP_NEW','GENERATE_OTP',
                    'GENERATE_OTP_EMAIL','SEND_PASSWORD_OTP','SUBMIT_PASSWORD_OTP','SET_NEW_PASSWORD','GET_MEDIA_DATA','GET_CODE_VALUES','PAYU_PAYMENT','GET_USER_SERVER','SUBMIT_USER_FEEDBACK','SEND_EMAIL','GET_LANDING_PROFILES',
                    'VERIFY_USER_NAME','SUBMIT_OTP_EMAIL','REGISTER_USER','RESEND_OTP'])

    def get_request_router(self):
        return SotrueAppUserRequestRouter()

    def get_app_name(self):
        return("sotrueapp")

    def get_message_mapper(self):
        return SotrueAppUserMessageMapper()

    def get_validator(self):
        return SotrueAppUserValidator()

    def get_auditor(self,session,connDB):
        return SotrueAppUserAuditRules(session,connDB)

    def get_ui_mapping(self):
        return SotrueAppUIMapping()

    def get_workflow_handler(self,connDB,session,audit):
        return SotrueAppWorkflow(connDB,session,audit)

    def is_service_controller(self):
        return True
