from components.AppRequestRouterBase import AppRequestRouterBase
from sotrueapp.application.handlers.SotrueAppUserHandler import SotrueAppUserHandler
from sotrueapp.application.handlers.SotrueAppAdminHandler import SotrueAppAdminHandler

class SotrueAppUserRequestRouter(AppRequestRouterBase):
    
    # Request routing rules
    routing_rules = dict(        
        APP_LOGIN=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.handle_login),
        GMAIL_LOGIN=AppRequestRouterBase.Handler(SotrueApp<PERSON>ser<PERSON>and<PERSON>,SotrueAppUserHandler.gmail_login),
        APPLE_LOGIN=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.apple_login),    
        APP_REGISTER=AppRequestRouterBase.Handler(SotrueAppUser<PERSON><PERSON><PERSON>,SotrueAppUserHandler.handle_register),
        SUBMIT_OTP=AppRequestRouterBase.Handler(SotrueApp<PERSON>ser<PERSON><PERSON><PERSON>,SotrueAppUserHandler.submit_otp),
        GET_USER_SERVER=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_server),
        GENERATE_OTP=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.generate_otp),
        SUBMIT_OTP_NEW=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_otp_new),
        GENERATE_OTP_EMAIL=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.generate_otp_email),
        SUBMIT_OTP_EMAIL=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_otp_email),
        VERIFY_USER_NAME=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.verify_user_name),
        REGISTER_USER=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.register_user),
        GET_INTEREST_LIST=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_interest_list),
        GET_INTEREST_CATEGORIES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_interest_categories),
        GET_USER_INTERESTS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_interests),
        UPDATE_USER_INTERESTS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_user_interest),
        UPDATE_USER_UI_COLOUR=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_user_ui_colour),
        RESEND_OTP=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.resend_otp),

        SEND_PASSWORD_OTP=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.send_password_otp),
        SUBMIT_PASSWORD_OTP=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_password_otp),
        SET_NEW_PASSWORD=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.set_new_password),
        CHANGE_PASSWORD=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.change_password),
        
        GET_CODE_VALUES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_code_values),
        
        SUBMIT_POST=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_post),
        DELETE_POST=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.delete_post),
        GET_POSTS_USER=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_posts_user),
        SUBMIT_POST_LIKE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_post_like),
        REMOVE_POST_LIKE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.remove_post_like),
        SUBMIT_POST_VIEW=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_post_view),        
        SUBMIT_POST_COMMENT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_post_comment),
        REMOVE_POST_COMMENT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.remove_post_comment),
        SUBMIT_COMMENT_COMMENT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_comment_comment),
        REMOVE_COMMENT_COMMENT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.remove_comment_comment),
        SUBMIT_COMMENT_LIKE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_comment_like),
        REMOVE_COMMENT_LIKE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.remove_comment_like),
        GET_POST_COMMENTS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_post_comments),
        GET_POSTS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_posts),
        GET_USER_POST=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_post),
        
        SAVE_USER_PROFILE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.save_user_profile),
        GET_USER_PROFILE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_profile),
        SAVE_ACCOUNT_SETTING=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.save_account_setting),
        GET_ACCOUNT_SETTING=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_account_setting),
        UPDATE_USER_LOCATION=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_user_location),
        GET_USER_REFERRAL_CODE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_referral_code),

        FOLLOW_PROFILE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.follow_profile),
        UNFOLLOW_PROFILE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.unfollow_profile),
        GET_FOLLOWERS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_followers),
        GET_FOLLOWER_COUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_follower_count),
        GET_FOLLOWING=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_following),
        GET_FOLLOWING_COUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_following_count),
        CHECK_USER_FOLLOWING=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.check_user_following),

        ADD_BOOKMARK=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.add_bookmark),
        REMOVE_BOOKMARK=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.remove_bookmark),
        GET_BOOKMARKS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_bookmarks),
        GET_BOOKMARK_COUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_bookmark_count),

        GET_PROFILE_SUGGESTIONS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_profile_suggestions),

        GET_MEDIA_DATA=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_media_data),
        GET_MEDIA_URL_POST=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_media_url_post),
        GET_MEDIA_URL_PROFILE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_media_url_profile),
        GET_MEDIA_COUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_media_count),

        SUBMIT_STORY=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_story),
        DELETE_STORY=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.delete_story),
        GET_STORIES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_stories),
        GET_PROFILE_STORIES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_profile_stories),

        GET_PRIVACY_SETTINGS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_privacy_settings),
        UPDATE_PRIVACY_SETTINGS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_privacy_settings),

        DO_SEARCH=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.do_search),
        DO_PLAYLIST_SEARCH=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.do_playlist_search),
        DO_TOPIC_SEARCH=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.do_topic_search),
        TYPE_AHEAD_SEARCH=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.type_ahead_search),

        GET_PAYMENT_ORDER=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payment_order),
        GET_PAYMENT_HASH=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payment_hash),
        UPDATE_PAYMENT_ORDER=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_payment_order),
        PAYU_PAYMENT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payu_response),
        GET_PAYMENT_STATUS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payment_status),
        GET_PROFILE_BALANCE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_profile_balance),

        GET_SUBSCRIBE_DETAILS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_subscribe_details),
        SUBSCRIBE_PROFILE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.subscribe_profile),
        GET_SUBSCRIBED_PROFILES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_subscribed_profiles),
        SUBSCRIBE_POST=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.subscribe_post),
        GET_SUBSCRIBED_POSTS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_subscribed_posts),
        UNSUBSCRIBE_PROFILE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.unsubscribe_profile),
        GET_PAYMENTS_CREDIT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payments_credit),
        GET_PAYMENTS_DEBIT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payments_debit),
        GET_USER_PROFILE_SUBSCRIBERS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_profile_subscribers),        
        GET_USER_PROFILE_SUBSCRIBER_PAID=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_profile_subscribers_paid),
        GET_USER_POST_SUBSCRIBERS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_post_subscribers),
        GET_SUBSCRIBER_COUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_subscriber_count),

        REPORT_ISSUE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.report_issue),
        BLOCK_ACCOUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.block_account),
        RESTRICT_ACCOUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.restrict_account),        
        GET_BLOCKED_ACCOUNTS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_blocked_accounts),
        GET_RESTRICTED_ACCOUNTS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_restrcited_accounts),
        UNBLOCK_ACCOUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.unblock_account),
        UNRESTRICT_ACCOUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.unrestrict_account),
        UPLOAD_ACCOUNT_VERIFICATION=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.upload_account_verification),
        UPLOAD_ACCOUNT_VERIFICATION_FIRM=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.upload_account_verification_firm),

        DELETE_ACCOUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.delete_account),
        GET_PAYMENT_SUMMARY_VALUES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payment_summary_values),

        GET_NOTIFICATIONS_LIKES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_notifications_likes),
        GET_NOTIFICATIONS_COMMENTS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_notifications_comments),
        GET_NOTIFICATIONS_SUBSCRIBES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_notifications_subscribes),
        GET_NOTIFICATIONS_FOLLOWS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_notifications_follows),
        GET_NOTIFICATIONS_TAG=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_notifications_tag),

        GET_ALERT_SETTINGS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_alert_settings),
        UPDATE_ALERT_SETTINGS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_alert_settings),

        GET_USER_MESSAGES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_messages),
        UPDATE_MESSAGE_STATUS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_message_status),
        CHECK_USER_HANDLE=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.check_user_handle),        

        CHECK_REMOTE_DUPLICATE_USER=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.check_remote_duplicate_user_test),
        GET_REFERRAL_EARNED_USERS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_referral_earned_users),

        GET_PAYOUT_HISTORY=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payout_history),
        GET_PAYOUT_DETAILS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_payout_details),
        SUBMIT_PAYOUT_REQUEST=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_payout_request),
        GET_POST_LIKE_USERS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_post_like_users),
        SEARCH_POST_LIKE_USERS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.search_post_like_users),

        SUBMIT_USER_FEEDBACK=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.submit_user_feedback),
        DELETE_USER_POST_COMMENT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.delete_user_post_comment),
        UPDATE_POST_COMMENT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_post_comments),
        SAVE_FCM_KEY=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.save_fcm_key),

        GET_USER_SEQ=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_seq_for_handle),        
        GET_TAGGED_USERS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_tagged_users),
        GET_POSTS_TAGGED=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_posts_tagged),
        GET_USER_TO_TAG=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_user_to_tag),
        VALIDATE_TAGGING=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.validate_tagging),        

        GET_STORY_DETAILS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_story_details),        
        UPDATE_NOTIFICATION_VIEW=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_notification_view),
        GET_NOTIFICATION_STATUS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_notification_status),
        GET_FINANCIAL_REPORT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_financial_report),

        GET_POPULAR_CATEGORIES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_popular_categories),

        SEND_EMAIL=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.send_email),
        GET_LANDING_PROFILES=AppRequestRouterBase.Handler(SotrueAppAdminHandler,SotrueAppAdminHandler.get_landing_profiles),        

        GET_PAY_ORDER_STATUS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_pay_order_status),
        UPDATE_SHARE_COUNT=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_share_count),

        UPDATE_VERIFY_NOTIFY=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.update_verify_notify),
        GET_LOCATION_LIST=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_location_list),

        GET_PLAYLIST_SHOWS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_playlist_shows),
        GET_PLAYLIST_EPISODES=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_playlist_episodes),
        GET_PLAYLIST_CLIPS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_playlist_clips),
        GET_GENRE_TOPIC_MASTERS=AppRequestRouterBase.Handler(SotrueAppUserHandler,SotrueAppUserHandler.get_genre_topic_masters),
    )


#
# Test routine
#
if __name__ == "__main__":
    pass    
