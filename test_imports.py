#!/usr/bin/env python3
"""
Test script to verify that all imports work correctly
This can be run in the Docker container to verify the deployment
"""

import sys
import os

# Add the sotruebackend directory to Python path
sys.path.insert(0, '/app/sotruebackend')

# Set Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sotruebackend.settings')

try:
    import django
    django.setup()
    print("✓ Django setup successful")
    
    # Test the problematic import
    from sotrueapp.application.controllers import SotrueAppUserController
    from sotrueapp.application.controllers import SotrueAppAdminController
    from sotrueapp.application.controllers import SotrueAppPlayController
    print("✓ Controller imports successful")
    
    # Test URL configuration
    from django.urls import reverse
    from django.conf import settings
    print("✓ Django configuration successful")
    
    print("✓ All imports working correctly!")
    
except Exception as e:
    print(f"✗ Import failed: {e}")
    sys.exit(1)
