# SoTrue Backend

## Docker Setup

This project is containerized using Docker. Follow these instructions to set up and run the application.

### Prerequisites

- Docker
- Docker Compose

### Getting Started

1. **Clone the repository**

```bash
git clone https://github.com/varenyamnikam/sotrue-backend.git
cd sotrue-backend
```

2. **Set up environment variables**

Copy the example environment file and modify it if needed:

```bash
cp .env.example .env
```

3. **Build and start the containers**

```bash
docker-compose up -d --build
```

This will:

- Build the Docker image for the web application
- Start the PostgreSQL database container
- Start the web application container
- Initialize the database with the provided SQL dump

4. **Access the application**

The application will be available at: http://localhost:8000

### Managing the Application

- **View logs**

```bash
docker-compose logs -f
```

- **Stop the containers**

```bash
docker-compose down
```

- **Stop the containers and remove volumes**

```bash
docker-compose down -v
```

- **Run Django management commands**

```bash
docker-compose exec web python manage.py [command]
```

Example:

```bash
docker-compose exec web python manage.py migrate
docker-compose exec web python manage.py createsuperuser
```

### Database

The PostgreSQL database is accessible at:

- Host: localhost
- Port: 5432
- Database: sotruedb_yf96
- Username: varenyam
- Password: (as specified in .env file)

## Docker Architecture and Operation

### 1. Containerization Architecture

The SoTrue Backend application is containerized using a multi-container architecture with Docker and Docker Compose. Here's how the containerization works:

#### Web Application Container (`sotrue-backend`)

- **Base Image**: Python 3.10 slim
- **Size**: Approximately 1.81GB
- **Main Components**:
  - Django application code
  - Python dependencies (installed via pip)
  - System dependencies (ffmpeg, libpq-dev, libmagic1, etc.)
  - Gunicorn as the WSGI HTTP server
- **Entry Point**: A custom `docker-entrypoint.sh` script that:
  1. Waits for the database to be ready
  2. Applies database migrations
  3. Starts the Gunicorn server

#### Database Container (`sotrue-db`)

- **Image**: PostgreSQL 14
- **Persistence**: Uses a Docker volume to persist data
- **Initialization**: Automatically imports a SQL dump during first startup

### 2. Networks, Database, and File System

#### Docker Network

- A dedicated bridge network (`sotruebackend_default`) is created to allow containers to communicate
- The web container can reach the database container using the service name as hostname (`sotrue-db`)

#### Database Configuration

- The database connection is configured through environment variables
- The application uses either:
  - The local PostgreSQL container for development
  - A remote Render PostgreSQL instance for production (configured via DATABASE_URL)

#### File System and Volumes

- **Database Volume**: Persists PostgreSQL data between container restarts
- **Application Code**: Mounted directly from the host during development
- **Static Files**: Served by Django in development, should use a proper static file server in production
- **Media Files**: Stored in a dedicated directory, accessible by the application
- **Logs**: Written to `/tmp/sotruelogs` (may require directory creation on the host)

### 3. Operating the Containerized Application

#### Basic Operations

**Starting the Application**

```bash
# Start with logs in the foreground
docker compose up

# Start in detached mode (background)
docker compose up -d
```

**Stopping the Application**

```bash
# Stop containers but keep volumes
docker compose down

# Stop containers and remove volumes (will delete database data)
docker compose down -v
```

**Rebuilding After Changes**

```bash
# Rebuild and restart containers
docker compose up -d --build
```

#### Maintenance Operations

**Viewing Logs**

```bash
# View logs from all containers
docker compose logs

# View logs from a specific container with follow
docker logs -f sotrue-backend
```

**Accessing the Database**

```bash
# Connect to the database using psql
docker compose exec sotrue-db psql -U varenyam -d sotruedb_yf96

# Backup the database
docker compose exec sotrue-db pg_dump -U varenyam -d sotruedb_yf96 > backup.sql

# Restore the database
cat backup.sql | docker compose exec -T sotrue-db psql -U varenyam -d sotruedb_yf96
```

**Running Django Management Commands**

```bash
# Run migrations
docker compose exec sotrue-backend python sotruebackend/manage.py migrate

# Create a superuser
docker compose exec sotrue-backend python sotruebackend/manage.py createsuperuser

# Collect static files
docker compose exec sotrue-backend python sotruebackend/manage.py collectstatic
```

**Accessing the Container Shell**

```bash
# Access the web container shell
docker compose exec sotrue-backend bash

# Access the database container shell
docker compose exec sotrue-db bash
```

#### Deployment Considerations

**Building for Production**

- Use multi-stage builds to reduce image size
- Consider using a dedicated static file server (Nginx, S3)
- Set appropriate environment variables for production

**Pushing to a Registry**

```bash
# Tag the image
docker tag sotruebackend-web:latest yourusername/sotrue-backend:latest

# Push to Docker Hub
docker push yourusername/sotrue-backend:latest
```

**Deploying on Render**

1. Push your image to a container registry
2. Create a new Web Service on Render
3. Select "Deploy an existing image from a registry"
4. Configure environment variables
5. Set the port to 8000

### Troubleshooting

- If you encounter permission issues with log files, ensure the `/tmp/sotruelogs` directory exists and has appropriate permissions:

```bash
mkdir -p /tmp/sotruelogs
chmod 777 /tmp/sotruelogs
```

- If the database fails to initialize, you can manually import the SQL dump:

```bash
docker-compose exec db psql -U varenyam -d sotruedb_yf96 -f /docker-entrypoint-initdb.d/sotruedbdump.sql
```

- If Docker Desktop is not running, start it from the Start menu or system tray

- If containers won't start due to port conflicts, check if other services are using ports 8000 or 5432:

```bash
# On Windows
netstat -ano | findstr :8000
netstat -ano | findstr :5432
```

Audit->Validate
if no access token action_override
