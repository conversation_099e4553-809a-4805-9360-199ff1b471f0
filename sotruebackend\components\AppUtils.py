import json
import sys
from datetime import datetime, timedelta
import shutil
import zipfile
import os
import time
from .AppConfigs import AppConfigs
from dateutil import parser
import calendar
from dateutil.relativedelta import relativedelta
import socket
import platform
from PIL import Image, ImageOps
import urllib
import cv2
import numpy
import locale
# Removed moviepy dependency
import uuid
import ffmpeg

class AppUtils:

    @staticmethod
    def set_time_zone():
        if platform.system() != "Windows":
            os.environ["TZ"] = AppConfigs.time_zone
            time.tzset()
            #print(datetime.today().strftime('%Y-%m-%d %H:%M:%S'))

    #
    # Utility function to check if the input string starts with the specified word
    # case insensitive
    #
    @staticmethod
    def starts_with(input_str,word):

        delims = (" ","\t","\n")
        word = word.lower()

        for delim in delims:
            pos = input_str.find(delim)
            if pos != -1:
                match = (input_str[:pos]).lower()
                if match == word:
                    return True

        return False

    #
    # Utility function to check if the input string is present as an
    # independent word in the search string
    #
    @staticmethod
    def contains(input_str,word):

        word = word.lower()
        input_str = input_str.lower()

        pos = input_str.find(word)
        if pos != -1:
            start = pos-1
            end = pos+len(word)+1
            if start < 0:
                start = 0

            match = (input_str[start:end]).strip()
            if match == word:
                    return True

        return False

    #
    # Utility function to parse a json input
    #
    @staticmethod
    def parse_json(input_json):
        json_data = json.loads(input_json)
        return json_data

    #
    # Utility function to convert a dict to json
    #
    @staticmethod
    def to_json(input_data):
        json_str = json.dumps(input_data,default=str)
        return json_str

    #
    # Utility function to return the base path
    #
    @staticmethod
    def get_base_path():
        base_path = sys.path[0]
        return base_path

    #
    # Method that converts a list of named tuples to list of dicts
    #
    @staticmethod
    def convert_tuples_to_dicts(tuples_list):

        dict_list = []
        for row in tuples_list:
            dict_list.append(dict(row._asdict()))
        return dict_list

    #
    # Get today's date in yyyy-mm-dd format as a string
    #
    @staticmethod
    def get_today_date_str():
        return datetime.today().strftime('%Y-%m-%d')

    #
    # Get today's date
    #
    @staticmethod
    def get_today_date():
        return datetime.today()

    #
    # Get today's date in yyyy-mm-dd format as a string
    #
    @staticmethod
    def get_cur_timestamp():
        return datetime.today().strftime('%Y-%m-%d %H:%M:%S')

    #
    # Get today's date in yyyy_mm_dd_HH_MM_SS format as a string
    #
    @staticmethod
    def get_cur_timestamp_flat():
        return datetime.today().strftime('%Y_%m_%d_%H_%M_%S')

    #
    # Returns the time as a float
    #
    @staticmethod
    def get_time():
        return time.time()

    #
    # Convert a sequence of strings to a single delimited string
    #
    @staticmethod
    def convert_to_delim_str(inlist, delim, quote=""):
        ret_val = ""
        for item in inlist:
            if ret_val:
                ret_val += delim
            ret_val += quote + str(item) + quote
        return(ret_val)

    #
    # Copy a file from one location to another
    #
    @staticmethod
    def copy_files(src,dest):
        shutil.copy(src, dest)

    #
    # Function to check if the path passed is an existing directory
    #
    @staticmethod
    def folder_exists(path):
        return os.path.isdir(path);

    #
    # Unzip the zip file path in the specified directory
    #
    @staticmethod
    def unzip(file_path,unzip_path):
        with zipfile.ZipFile(file_path, 'r') as zip_ref:
            zip_ref.extractall(unzip_path)
            file_names = zip_ref.namelist()
            return file_names


    #
    # Method to create a temp folder
    #
    @staticmethod
    def create_temp_folder(temp_folder):
        folder_path = os.path.join(AppConfigs.temp_folder_path,temp_folder)
        os.mkdir(folder_path)

        return(folder_path)

    #
    # Method to create a temp folder
    #
    @staticmethod
    def create_folder(folder_path):
        # Use makedirs to create all intermediate directories if needed
        import os
        os.makedirs(folder_path, exist_ok=True)
        return(folder_path)

    #
    # Change date format to yyyy-mm-dd
    #
    @staticmethod
    def format_date(in_date):
        return parser.parse(in_date).strftime('%Y-%m-%d')

    @staticmethod
    def format_date_display(in_date):
        return parser.parse(in_date).strftime('%d-%b-%Y')

    @staticmethod
    def format_ts_display(in_date):
        return parser.parse(in_date).strftime('%d-%b-%Y %I:%M %p')

    @staticmethod
    def get_current_ts_display():
        return datetime.today().strftime('%d-%b-%Y %H:%M:%S')

    @staticmethod
    def get_current_date_report():
        return datetime.today().strftime('%d-%m-%Y')

    @staticmethod
    def format_date_report(in_date):
        return parser.parse(in_date).strftime('%d-%m-%Y')

    @staticmethod
    def get_time_display(in_time):
        return parser.parse(in_time).strftime('%I:%M %p')

    @staticmethod
    def get_month_range():
        return (datetime.today() - timedelta(days=30)).strftime('%Y-%m-%d')

    @staticmethod
    def advance_date_by(in_date,days):
        return (parser.parse(in_date) + timedelta(days=days)).strftime('%Y-%m-%d')

    @staticmethod
    def retard_date_by(in_date,days):
        return (parser.parse(in_date) - timedelta(days=days)).strftime('%Y-%m-%d')

    @staticmethod
    def advance_date_by_hrs(in_date,hrs):
        return (parser.parse(in_date) + timedelta(hours=hrs)).strftime('%Y-%m-%d')

    @staticmethod
    def get_date_diff_days_str(from_date, to_date):
        from_date = parser.parse(from_date)
        to_date = parser.parse(to_date)
        diff = to_date - from_date
        return(diff.days)

    @staticmethod
    def date_diff_years(start_date, end_date):
        start_date = parser.parse(start_date)
        end_date = parser.parse(end_date)
        return(relativedelta(end_date, start_date).years)

    @staticmethod
    def date_diff_hrs(start_date, end_date):
        start_date = parser.parse(start_date)
        if end_date.find(" ") == -1:
            end_date = end_date + " 23:59:59"
        end_date = parser.parse(end_date)
        diff = end_date - start_date
        rel = relativedelta(end_date, start_date)
        return((diff.days*24) + rel.hours + (rel.minutes/60))

    @staticmethod
    def time_diff_seconds(start_time, end_time):
        t1 = datetime.strptime(start_time, "%H:%M:%S")
        t2 = datetime.strptime(end_time, "%H:%M:%S")
        return ((t2 - t1).seconds)

    @staticmethod
    def ts_diff_seconds(start_date, end_date):
        start_date = parser.parse(start_date)
        if end_date.find(" ") == -1:
            end_date = end_date + " 23:59:59"
        end_date = parser.parse(end_date)
        diff = end_date - start_date
        rel = relativedelta(end_date, start_date)
        return((diff.days*24*60*60) + rel.hours*60*60 + (rel.minutes*60) + rel.seconds)

    @staticmethod
    def compute_age(start_date, end_date):
        start_date = parser.parse(start_date)
        end_date = parser.parse(end_date)
        return((end_date - start_date).__str__())

    @staticmethod
    def get_time_due(in_date,hrs):
        target = parser.parse(in_date) + timedelta(hours=hrs)
        today = datetime.today()
        ret_val = None
        if today < target:
            diff = relativedelta(target,today)
            value = str(diff.hours + (diff.days*24)) + ":" + str(diff.minutes) + ":" + str(diff.seconds)
            ret_val = {"diff":value,"mode":"decr"}
        else:
            diff = relativedelta(today,target)
            value = str(diff.hours + (diff.days*24)) + ":" + str(diff.minutes) + ":" + str(diff.seconds)
            ret_val = {"diff":value,"mode":"incr"}
        return ret_val

    def get_time_past_due(in_date,hrs):
        target = parser.parse(in_date) + timedelta(hours=hrs)
        today = datetime.today()

        diff = relativedelta(today,target)
        value = ""
        if diff.months:
            value += str(diff.months) + " Months "
        if diff.days:
            value += str(diff.days) + " Days "

        value += str(diff.hours) + ":" + str(diff.minutes) + " Hrs"
        return value


    @staticmethod
    def quote_input(inp_list):
        response = ""
        for item in inp_list:
            if response:
                response += ","
            response += "'" + item + "'"
        return(response)

    @staticmethod
    def get_current_week():
        dt = datetime.today()
        start = dt - timedelta(days=dt.weekday())
        end = start + timedelta(days=6)
        ret_val = {"start":start.strftime('%Y-%m-%d'),"end":end.strftime('%Y-%m-%d')}
        return ret_val

    @staticmethod
    def get_current_month():
        date = datetime.today()
        days = calendar.monthrange(date.year,date.month)
        ret_val = {"start":(datetime(date.year,date.month,1)).strftime('%Y-%m-%d'),
                   "end":(datetime(date.year,date.month,days[1])).strftime('%Y-%m-%d')}
        return ret_val

    @staticmethod
    def get_last_week():
        dt = datetime.today()
        start = dt - timedelta(days=7)
        start = start - timedelta(days=dt.weekday())
        end = start + timedelta(days=6)
        ret_val = {"start":start.strftime('%Y-%m-%d'),"end":end.strftime('%Y-%m-%d')}
        return ret_val

    @staticmethod
    def get_last_month():
        date = datetime.today() - relativedelta(months=1)
        days = calendar.monthrange(date.year,date.month)
        ret_val = {"start":(datetime(date.year,date.month,1)).strftime('%Y-%m-%d'),
                   "end":(datetime(date.year,date.month,days[1])).strftime('%Y-%m-%d')}
        return ret_val

    @staticmethod
    def get_date_past(days):
        return (datetime.today() - timedelta(days=days)).strftime('%Y-%m-%d')

    @staticmethod
    def get_ts_back_by(ts, hrs):
        return (parser.parse(ts) - timedelta(hours=hrs)).strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    def get_ts_back_by_mins(ts, mins):
        return (parser.parse(ts) - timedelta(minutes=mins)).strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    def get_ts_ahead_by(ts, hrs):
        return (parser.parse(ts) + timedelta(hours=hrs)).strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    def get_ts_ahead_by_sec(ts, sec):
        return (parser.parse(ts) + timedelta(seconds=sec))

    @staticmethod
    def replace_none(row):
        return(['' if v is None else v for v in row])

    @staticmethod
    def format_decimal(value):
        return("{:.2f}".format(value))

    @staticmethod
    def convert_hours(value):
        minutes = int(value*60)

        hours = minutes // 60
        minutes = minutes % 60
        days = hours // 24
        hours = hours % 24

        retval = "";
        if days:
            if int(days) != 1:
                retval += str(days) + " Days "
            else:
                retval += str(days) + " Day "
        retval += str(hours) + ":" + str(minutes) + " hrs"
        return retval

    @staticmethod
    def get_weekends_between(from_date, to_date, weekends):
        day_map = dict(zip(calendar.day_name,range(7)))
        from_date = parser.parse(from_date)
        to_date = parser.parse(to_date)

        compute_days = []
        for day in weekends:
            delta = day_map[day] - from_date.weekday()
            first_date = from_date + timedelta(days=delta)
            if delta < 0:
                first_date += timedelta(days=7)
            while first_date <= to_date:
                compute_days.append(first_date.strftime('%Y-%m-%d'))
                first_date += timedelta(days=7)
        return(compute_days)

    @staticmethod
    def convert_to_hrs(minutes):
        hrs = minutes // 60
        minutes = minutes - (hrs*60)
        if hrs:
            if hrs > 1:
                return (str(hrs) + " hrs " +
                        ("" if not minutes else str(minutes) + " mins"))
            else:
                return (str(hrs) + " hr " +
                        ("" if not minutes else str(minutes) + " mins"))
        else:
            return (str(minutes) + " mins")


    @staticmethod
    def get_elpased_days(start_date):
        end_date = datetime.today().strftime('%Y-%m-%d %H:%M:%S')
        prev_day_begin = (parser.parse(end_date) - timedelta(days=1)).strftime('%Y-%m-%d') + " 00:00:00"

        start_date = parser.parse(start_date)
        end_date = parser.parse(end_date)
        prev_day_begin = parser.parse(prev_day_begin)

        diff = end_date - start_date
        rel = relativedelta(end_date, start_date)
        diff_begin = ((end_date - prev_day_begin).days*24) + relativedelta(end_date, prev_day_begin).hours

        diff_hrs = (diff.days*24) + rel.hours
        print(diff_hrs)
        print(diff_begin)
        print(rel.hours)

        if diff_hrs < 24 and diff_hrs < diff_begin:
            if rel.hours:
                return str(rel.hours) + " hrs ago"
            else:
                return str(rel.minutes) + " mins ago"

        if diff_hrs < diff_begin:
            return "Yesterday"
        else:
            days = int(diff_hrs) // 24
            return str(days+1) + " days ago"


    @staticmethod
    def get_time_for_file():
        return datetime.today().strftime('%d%b%Y_%H%M%S')

    @staticmethod
    def get_server_ip():
        hostname = socket.gethostname()
        ip_address = socket.gethostbyname(hostname)
        return ip_address


    @staticmethod
    def resize_image(file_path):
        img = Image.open(file_path)
        format = None
        out_path = file_path[0:file_path.rfind(".")+1] + img.format.lower()
        size = img.size
        d1 = size[0]
        d2 = size[1]
        if d1 > d2 and d1 > 1024:
            format = img.format.lower()
            d2 = int(d2 * (1024/d1))
            d1 = 1024
            img = img.resize((d1,d2))
            img.save(out_path)
        elif d2 > d1 and d2 > 1024:
            format = img.format.lower()
            d1 = int(d1 * (1024/d2))
            d2 = 1024
            img = img.resize((d1,d2))
            img.save(out_path)
        if not format:
            format = file_path[file_path.rfind(".")+1:]
        return(format)


    @staticmethod
    def rotate_image(filepath,angle):
        image=Image.open(filepath)
        if image._getexif():
            image = ImageOps.exif_transpose(image)
        else:
            image=image.rotate(angle, expand=True)

            image.save(filepath)
            image.close()

    @staticmethod
    def delete_file(filepath):
        try:
            os.remove(filepath)
        except OSError:
            pass

    @staticmethod
    def get_formatted_time_past(in_date):
        today = datetime.today()
        target = parser.parse(in_date)
        diff = relativedelta(today,target)

        if diff.minutes==0 and diff.hours == 0 and diff.days == 0:
            return( str(diff.seconds) + " seconds ago" if diff.seconds > 1 else "1 second ago" )
        elif diff.days == 0 and diff.hours == 0:
            return( str(diff.minutes) + " minutes ago" if diff.minutes > 1 else "1 minute ago" )
        elif diff.days == 0:
            return( str(diff.hours+1) + " hours ago" if diff.hours >= 1 and diff.minutes>=30 else str(diff.hours) + " hours ago" if diff.hours > 1 else "1 hour ago" )
        elif diff.days < 7:
            return( str(diff.days+1) + " days ago" if diff.days >= 1 and diff.hours >=12 else str(diff.days) + " days ago" if diff.days > 1 else "1 day ago" )
        else:
            return(parser.parse(in_date).strftime('%d %b'))


    @staticmethod
    def get_file_size(file_path):
        return(os.path.getsize(file_path))


    @staticmethod
    def url_encode(value):
        return urllib.parse.quote_plus(value)

    @staticmethod
    def get_first_video_frame(video_file,frame_file):
        vidcap = cv2.VideoCapture(video_file)
        count = 0
        success,image = vidcap.read()
        while not success and count<10:
            success,image = vidcap.read()
            count += 1
        if success:
            meta_dict = ffmpeg.probe(video_file)
            rotate = None
            if meta_dict['streams'][0]['codec_type'] == 'video':
                if "rotate" in meta_dict['streams'][0]['tags']:
                    rotate = meta_dict['streams'][0]['tags']['rotate']
            elif len(meta_dict) == 2 and meta_dict['streams'][1]['codec_type'] == 'video':
                if "rotate" in meta_dict['streams'][1]['tags']:
                    rotate = meta_dict['streams'][1]['tags']['rotate']
            if rotate:
                rotate_code = cv2.ROTATE_180
                image = cv2.rotate(image, rotate_code)
            cv2.imwrite(frame_file, image)
            return True
        else:
            return False

    @staticmethod
    def format_currency(value):
        try:
            locale.setlocale(locale.LC_MONETARY, 'en_IN')
            return locale.currency(value, grouping=True)
        except locale.Error:
            # Fallback if locale is not available
            try:
                locale.setlocale(locale.LC_MONETARY, '')  # Use system default locale
                return locale.currency(value, grouping=True)
            except locale.Error:
                # If all else fails, format manually
                return f"₹ {value:,.2f}"

    @staticmethod
    def watermark_image(image_file,watermark_file,output_file):
        image = Image.open(image_file)
        water_mark = Image.open(watermark_file)

        width,height = image.size
        w_width,w_height = water_mark.size

        t_width = width * 0.05
        t_height = (t_width/w_width)*w_height
        water_mark = water_mark.resize((int(t_width), int(t_height)))

        image.paste(water_mark, (int(width-t_width), int(height-t_height)))
        image.save(output_file)

        image = cv2.imread(image_file)
        water_mark = cv2.imread(output_file)
        alpha = 0.5
        cv2.addWeighted(water_mark, alpha, image, (1 - alpha),0, image)
        cv2.imwrite(output_file,image)


    @staticmethod
    def validate_video_file(video_file):
        vidcap = cv2.VideoCapture(video_file)
        success,image = vidcap.read()
        if not success:
            vidcap.release()
            del vidcap
            # Skip moviepy processing as it's not compatible
            # Just use ffmpeg directly
            extn = "" if video_file.rfind(".") == -1 else video_file[video_file.rfind("."):]
            target_file = AppUtils.get_cur_timestamp_flat() + "_tmp" + extn
            # Use ffmpeg to extract a subclip
            os.system(f"ffmpeg -i {video_file} -ss 0.1 -t 10 -c copy {target_file}")
            AppUtils.copy_files(target_file,video_file)
            AppUtils.delete_file(target_file)

    @staticmethod
    def get_video_duration(video_file):
        # Use OpenCV to get video duration
        data = cv2.VideoCapture(video_file)
        frames = data.get(cv2.CAP_PROP_FRAME_COUNT)
        fps = int(data.get(cv2.CAP_PROP_FPS))
        seconds = int(frames / fps) if fps else 0
        return(seconds)

    @staticmethod
    def blur_image(image_file, blur_file):
        img = cv2.imread(image_file)
        blurImg = cv2.blur(img,(75,75))
        cv2.imwrite(blur_file,blurImg)


    @staticmethod
    def get_year_months(start):
        temp = start.split("-")
        start_year = int(temp[0])
        start_month = int(temp[1])
        end = datetime.today().strftime('%Y-%m')
        temp = end.split("-")
        end_year = int(temp[0])
        end_month = int(temp[1])
        retval = []
        while(True):
            retval.append(str(start_year) + "-" + (str(start_month) if start_month > 9 else "0" + str(start_month)))
            start_month += 1
            if start_month > 12:
                start_month = 1
                start_year += 1
            if start_year > end_year:
                break
            elif start_year == end_year and start_month > end_month:
                break
        return(retval)

    @staticmethod
    def get_cur_year_month():
        return(datetime.today().strftime('%Y-%m'))

    @staticmethod
    def incr_month(start):
        temp = start.split("-")
        start_year = int(temp[0])
        start_month = int(temp[1])
        start_month += 1
        if start_month > 12:
            start_month = 1
            start_year += 1
        return(str(start_year) + "-" + (str(start_month) if start_month > 9 else "0" + str(start_month)))

    @staticmethod
    def convert_month_name(indate):
        return(parser.parse(indate).strftime('%B, %Y'))

    @staticmethod
    def get_fiscal():
        cur_year = int(datetime.today().strftime('%Y'))
        cur_month = int(datetime.today().strftime('%m'))
        if cur_month < 4:
            cur_year -= 1
        dates = {"start":str(cur_year)+"-04-01","end":str(cur_year+1)+"-03-31"}
        return dates

    @staticmethod
    def get_uuid_str():
        return(str(uuid.uuid4()))

#
# Testing code
#
if __name__ == "__main__":
    pass
