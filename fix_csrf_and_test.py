#!/usr/bin/env python3
"""
Script to force rebuild Docker containers and test CSRF fix
"""

import subprocess
import time
import requests
import sys

def run_command(command, description):
    """Run a shell command and print the result"""
    print(f"\n🔧 {description}")
    print(f"Running: {command}")
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True, timeout=300)
        if result.returncode == 0:
            print(f"✅ {description} completed successfully")
            if result.stdout:
                print(f"Output: {result.stdout[-500:]}")  # Last 500 chars
        else:
            print(f"❌ {description} failed")
            print(f"Error: {result.stderr}")
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} timed out")
        return False
    except Exception as e:
        print(f"❌ {description} failed with exception: {e}")
        return False

def test_csrf_fix():
    """Test if CSRF fix is working"""
    print(f"\n🧪 Testing CSRF Fix")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test the problematic endpoint
    test_cases = [
        {
            "name": "GENERATE_OTP",
            "data": {"_action_code": "GENERATE_OTP", "mobile": "9420346490"}
        },
        {
            "name": "GET_CODE_VALUES", 
            "data": {"_action_code": "GET_CODE_VALUES"}
        }
    ]
    
    for test_case in test_cases:
        print(f"\n📡 Testing {test_case['name']}...")
        try:
            url = f"{base_url}/sotrueapp/appservice"
            response = requests.post(url, data=test_case['data'], timeout=10)
            
            print(f"   Status Code: {response.status_code}")
            
            if response.status_code == 403 and "CSRF" in response.text:
                print(f"   ❌ CSRF ERROR STILL EXISTS!")
                print(f"   Response: {response.text[:200]}")
                return False
            elif response.status_code in [200, 400, 500]:
                print(f"   ✅ CSRF FIXED! (Status {response.status_code} is expected)")
                try:
                    result = response.json()
                    print(f"   📊 Response: {result.get('status', 'Unknown')}")
                except:
                    print(f"   📊 Non-JSON response (normal for some endpoints)")
            else:
                print(f"   ⚠️  Unexpected status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Cannot connect to server - is it running?")
            return False
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            return False
    
    print(f"\n🎉 CSRF FIX VERIFICATION COMPLETE!")
    return True

def main():
    """Main function to rebuild and test"""
    print("🚀 SoTrue Backend CSRF Fix and Rebuild")
    print("=" * 60)
    
    # Step 1: Stop containers
    if not run_command("docker-compose down", "Stopping containers"):
        print("❌ Failed to stop containers")
        return False
    
    # Step 2: Clean up Docker
    run_command("docker system prune -f", "Cleaning Docker system")
    
    # Step 3: Remove specific images
    run_command("docker rmi sotruebackend-web 2>/dev/null || true", "Removing old images")
    
    # Step 4: Build with no cache
    if not run_command("docker-compose build --no-cache", "Building containers (no cache)"):
        print("❌ Failed to build containers")
        return False
    
    # Step 5: Start containers
    if not run_command("docker-compose up -d", "Starting containers"):
        print("❌ Failed to start containers")
        return False
    
    # Step 6: Wait for services to start
    print("\n⏳ Waiting for services to start...")
    time.sleep(15)
    
    # Step 7: Check container status
    run_command("docker-compose ps", "Checking container status")
    
    # Step 8: Check logs
    run_command("docker logs sotrue-backend --tail 20", "Checking backend logs")
    
    # Step 9: Test CSRF fix
    if test_csrf_fix():
        print("\n🎉 SUCCESS! CSRF issue has been resolved!")
        print("✅ Your API endpoints are now working without CSRF errors")
        return True
    else:
        print("\n❌ CSRF issue still exists. Check the logs above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
