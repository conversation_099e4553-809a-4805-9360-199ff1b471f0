# Exported from Render on 2025-05-11T13:37:13Z
services:
  - type: web
    name: sotrue-backend
    runtime: docker
    repo: https://github.com/varenyamnikam/sotrue-backend
    plan: free
    region: singapore
    dockerfilePath: ./Dockerfile
    healthCheckPath: /sotrueapp/health
    envVars:
      - key: ENVIRONMENT
        value: TEST
      - key: LOCAL_DB_HOST
        value: host.docker.internal
      - key: LOCAL_DB_USER
        value: postgres
      - key: LOCAL_DB_PASSWORD
        value: Varenyam@31
      - key: LOCAL_DB_NAME
        value: sotruedb
      - key: LOCAL_DB_PORT
        value: "5432"
      - key: TEST_DB_HOST
        value: dpg-d13ceo15pdvs73djkd6g-a.oregon-postgres.render.com
      - key: TEST_DB_PORT
        value: "5432"
      - key: TEST_DB_NAME
        value: sotruedb_sdip
      - key: TEST_DB_USER
        value: varenyam
      - key: TEST_DB_PASSWORD
        value: hKW4qfgb7iU7dLAKaiQrvFZ6WF9W93i9
      - key: ALLOWED_HOSTS
        value: "*"
      - key: LOG_FILE_PATH
        value: /tmp/sotruelogs/sotureapp.log
      - key: TEMP_FOLDER_PATH
        value: /tmp/sotruelogs/
      - key: SESSION_EXPIRY
        value: "900"
      - key: TIME_ZONE
        value: Asia/Calcutta
      - key: PORT
        value: "8000"
      - key: REDIS_HOST
        value: redis
      - key: REDIS_PORT
        value: "6379"
      - key: REDIS_DB
        value: "0"
      - key: DEBUG
        value: "True"
    startCommand: |
      set -ex
      cd sotruebackend
      python manage.py collectstatic --noinput
      python manage.py migrate
      gunicorn sotruebackend.wsgi:application --bind 0.0.0.0:$PORT --timeout 300 --workers 2 --threads 4
version: "1"
