"""
Custom middleware for SoTrue Backend
"""

from django.utils.deprecation import MiddlewareMixin
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator


class DisableCSRFMiddleware(MiddlewareMixin):
    """
    Disable CSRF protection for API endpoints
    """
    
    def process_request(self, request):
        # List of paths that should be exempt from CSRF
        csrf_exempt_paths = [
            '/sotrueapp/appservice',
            '/sotrueapp/adminservice',
            '/sotrueapp/playlist',
            '/sotrueapp/appservice'
        ]
        
        # Check if the current path should be exempt from CSRF
        if any(request.path.startswith(path) for path in csrf_exempt_paths):
            setattr(request, '_dont_enforce_csrf_checks', True)
        
        return None


class CORSMiddleware(MiddlewareMixin):
    """
    Add CORS headers for API requests
    """
    
    def process_response(self, request, response):
        # Add CORS headers for API endpoints
        api_paths = ['/sotrueapp/']
        
        if any(request.path.startswith(path) for path in api_paths):
            response['Access-Control-Allow-Origin'] = '*'
            response['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
            response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
            response['Access-Control-Allow-Credentials'] = 'true'
        
        return response
