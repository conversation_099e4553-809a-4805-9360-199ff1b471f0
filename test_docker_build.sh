#!/bin/bash

# Test script to build and test the Docker image locally
echo "Building Docker image..."
docker build -t sotrue-backend-test .

if [ $? -eq 0 ]; then
    echo "✓ Docker build successful"
    
    echo "Testing imports in container..."
    docker run --rm sotrue-backend-test python /app/test_imports.py
    
    if [ $? -eq 0 ]; then
        echo "✓ All tests passed!"
    else
        echo "✗ Import tests failed"
        exit 1
    fi
else
    echo "✗ Docker build failed"
    exit 1
fi
