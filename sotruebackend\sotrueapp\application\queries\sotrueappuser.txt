<query>
	<id>
        GET_USER_CREDENTIALS
	</id>
	<stmt>
            SELECT CAST(user_seq AS VARCHAR), password, status, CAST(last_profile AS VARCHAR), server, 
			is_gmail, email_id, is_apple, ui_colour
			FROM user_master WHERE email_id ILIKE %s AND status!='DELETED'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SERVER_DISTRIBUTION
	</id>
	<stmt>
            SELECT COUNT(user_seq), server FROM user_master GROUP BY server
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_EMAIL
	</id>
	<stmt>
        SELECT CAST(user_seq AS VARCHAR), status FROM user_master WHERE email_id ILIKE %s AND status!='DELETED'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_OTP
	</id>
	<stmt>
        UPDATE user_master SET activation_otp=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_EMAIL_ID
	</id>
	<stmt>
        SELECT email_id FROM user_master WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_USER_RECORD
	</id>
	<stmt>
        INSERT INTO user_master (full_name, email_id, password, activation_otp, status, 
		registered_on, comission_pc, server, is_gmail, is_apple,is_dummy) 
		VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(user_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_USER_RECORD_NEW
	</id>
	<stmt>
        INSERT INTO user_master (full_name, password, mobile_number, status, registered_on, comission_pc, server, is_dummy,
		mobile_otp) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(user_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_OTP
	</id>
	<stmt>
        SELECT activation_otp, full_name, email_id, mobile_number,
		CAST(used_referral_seq AS VARCHAR) FROM user_master WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_STATUS
	</id>
	<stmt>
        UPDATE user_master SET status=%s, activation_otp=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_DELETE_STATUS
	</id>
	<stmt>
        UPDATE user_master SET status=%s, activation_otp=%s, 
		delete_reason=%s, delete_comments=%s, other_delete_reason=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_PASSWORD
	</id>
	<stmt>
        UPDATE user_master SET password=%s, activation_otp=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_USER_ROLE
	</id>
	<stmt>
        INSERT INTO user_roles (user_seq, role_seq, status) VALUES (%s,%s,%s) RETURNING CAST(user_role_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_USER_PROFILE
	</id>
	<stmt>
        INSERT INTO user_profile (user_seq, user_handle, created_on, type, bank_ac_submitted, 
        			profile_balance, last_active_on, status, display_name, show_fan_count, show_media_count, enable_comment, enable_watermark) 
        VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(profile_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_USER_CATEGORY
	</id>
	<stmt>
        INSERT INTO profile_category (profile_seq, category) VALUES (%s,%s) RETURNING CAST(profile_cat_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_LAST_PROFILE
	</id>
	<stmt>
        UPDATE user_master SET last_profile=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_HANDLE
	</id>
	<stmt>
        SELECT CAST(profile_seq AS VARCHAR) FROM user_profile WHERE user_handle=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_HANDLE_SEQ
	</id>
	<stmt>
        SELECT CAST(profile_seq AS VARCHAR) FROM user_profile WHERE user_handle=%s AND profile_seq!=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_ROLE_SEQ
	</id>
	<stmt>
        SELECT CAST(role_seq AS VARCHAR) FROM roles_master WHERE role_name=%s and status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_CODE_VALUES
	</id>
	<stmt>
        SELECT config_key, display_value FROM config_meta_data WHERE config_type=%s AND status=%s
		ORDER BY display_value
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_CODE_DISPLAY
	</id>
	<stmt>
        SELECT display_value FROM config_meta_data WHERE config_key=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_POST
	</id>
	<stmt>
        INSERT INTO user_posts (profile_seq, post_comments, posted_on, post_type, viewer_fee, expire_on, status,
		is_playlist, episode_seq, location, schedule) 
		VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(post_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_POST_MEDIA
	</id>
	<stmt>
        INSERT INTO user_post_content (post_seq, media_file, media_size, media_type, status, media_cover, media_format, 
		video_duration, fuzzy_image, s3_enabled) 
		VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(content_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_POST_STATUS
	</id>
	<stmt>
        UPDATE user_posts SET status=%s WHERE post_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POSTS
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee, 
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, user_post_content.media_file, 
		user_post_content.media_type, CAST(user_posts.profile_seq AS VARCHAR), 
        user_post_content.media_cover, user_post_content.fuzzy_image,  user_post_content.s3_enabled,
		user_posts.episode_seq
		FROM user_posts, user_post_content, user_profile 
		WHERE user_posts.post_seq=user_post_content.post_seq AND user_posts.profile_seq=user_profile.profile_seq AND
		user_posts.profile_seq!=%s AND user_profile.status='ACTIVE' AND user_posts.is_playlist='NO' AND
		user_posts.status='ACTIVE' AND user_posts.expire_on>=%s AND user_posts.post_seq NOT IN 
		(SELECT post_reports.post_seq FROM post_reports WHERE post_reports.reported_by=%s AND post_reports.report_source='POST') 
		AND user_posts.profile_seq NOT IN 
		(SELECT restricted_profile FROM restricted_profiles WHERE restricted_by=%s AND status='ACTIVE' AND restriction_type='BLOCKED')
		AND user_post_content.media_type LIKE %s AND user_profile.is_verified LIKE %s <EXCLUDING_SEQ>
		<RESTRICTED_OTHERS>
		ORDER BY user_posts.posted_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SPECIAL
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee, 
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, user_post_content.media_file, 
		user_post_content.media_type, CAST(user_posts.profile_seq AS VARCHAR), 
        user_post_content.media_cover, user_post_content.fuzzy_image,  user_post_content.s3_enabled
		FROM user_posts, user_post_content, user_profile 
		WHERE user_posts.post_seq=user_post_content.post_seq AND user_posts.profile_seq=user_profile.profile_seq AND
		user_posts.post_seq=%s 
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POSTS_COUNT
	</id>
	<stmt>
        SELECT COUNT(user_posts.post_seq) FROM user_posts, user_post_content, user_profile 
		WHERE user_posts.profile_seq=user_profile.profile_seq AND user_posts.post_seq=user_post_content.post_seq AND 
		user_profile.status='ACTIVE' AND user_posts.profile_seq!=%s AND user_posts.status='ACTIVE' AND user_posts.expire_on>=%s    
		AND user_posts.post_seq NOT IN (SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='POST') 
		AND user_posts.profile_seq NOT IN 
		(SELECT restricted_profile FROM restricted_profiles WHERE restricted_by=%s AND status='ACTIVE' AND restriction_type='BLOCKED')
		AND user_post_content.media_type LIKE %s AND user_profile.is_verified LIKE %s <EXCLUDING_SEQ> <RESTRICTED_OTHERS>
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_BROWSING_HISTORY
	</id>
	<stmt>
        SELECT content_seqs FROM browsing_history WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_BROWSING_HISTORY
	</id>
	<stmt>
        UPDATE browsing_history SET content_seqs=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_BROWSING_HISTORY
	</id>
	<stmt>
        INSERT INTO browsing_history (profile_seq, content_seqs) VALUES (%s,%s) RETURNING CAST(history_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SEQ
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee, 
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, 
		user_post_content.media_file, user_post_content.media_type, 
		CAST(user_posts.profile_seq AS VARCHAR), 
        user_post_content.media_cover, user_post_content.s3_enabled FROM
        user_posts, user_post_content WHERE user_posts.post_seq=user_post_content.post_seq AND user_posts.post_seq=%s
		AND user_posts.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_PROFILE
	</id>
	<stmt>
        SELECT CAST(profile_seq AS VARCHAR) FROM user_posts WHERE post_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SEQ_DETAILS
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee, 
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, 
		user_post_content.media_file, user_post_content.media_type, 
		CAST(user_posts.profile_seq AS VARCHAR), 
        user_post_content.media_cover, user_post_content.fuzzy_image, user_post_content.s3_enabled
		FROM user_posts, user_post_content, user_profile 
		WHERE user_posts.post_seq=user_post_content.post_seq AND 
		user_posts.profile_seq=user_profile.profile_seq AND user_profile.status='ACTIVE' AND user_posts.post_seq=%s 
		AND user_posts.expire_on>=%s AND user_posts.post_seq NOT IN (SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='POST') 
		AND user_posts.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_POST_LIKES
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR), CAST(like_seq AS VARCHAR) 
		FROM post_likes WHERE liked_by_profile=%s AND post_seq IN (<POST_LIST>) AND post_likes.status='ACTIVE'
		AND post_likes.type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_POST_VIEWS
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR), CAST(view_seq AS VARCHAR) 
		FROM post_views WHERE liked_by_profile=%s AND post_seq IN (<POST_LIST>) AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_POST_COMMENTS
	</id>
	<stmt>
        SELECT CAST(content_seq AS VARCHAR), CAST(comment_seq AS VARCHAR) 
		FROM post_comments WHERE comment_profile_seq=%s AND content_seq IN (<POST_LIST>)
		AND content_type='POST' AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_LIKES
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR), CAST(like_seq AS VARCHAR)
		FROM post_likes WHERE liked_by_profile=%s AND post_seq IN (<POST_LIST>) AND post_likes.status='ACTIVE'
		AND type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_VIEWS
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR), CAST(view_seq AS VARCHAR)
		FROM post_views WHERE liked_by_profile=%s AND post_seq IN (<POST_LIST>) AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SEQ_LIKE
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR), CAST(like_seq AS VARCHAR)
		FROM post_likes WHERE liked_by_profile=%s AND post_seq=%s AND post_likes.status='ACTIVE'
		AND type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_COMMENT_SEQ_LIKE
	</id>
	<stmt>
        SELECT CAST(content_seq AS VARCHAR), CAST(comment_seq AS VARCHAR)
		FROM post_comments WHERE comment_profile_seq=%s AND content_seq=%s
		AND content_type='POST' AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_BOOKMARKS
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR), CAST(bookmark_seq AS VARCHAR)
		FROM profile_bookmarks WHERE profile_seq=%s AND post_seq IN (<POST_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SEQ_BOOKMARK
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR), CAST(bookmark_seq AS VARCHAR) 
		FROM profile_bookmarks WHERE profile_seq=%s AND post_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_LIKE_COUNTS
	</id>
	<stmt>
        SELECT COUNT(like_seq), CAST(post_seq AS VARCHAR)
		FROM post_likes WHERE post_seq IN (<POST_LIST>) AND status='ACTIVE' AND type=%s
		GROUP BY post_seq
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_VIEWS_COUNTS
	</id>
	<stmt>
        SELECT COUNT(view_seq), CAST(post_seq AS VARCHAR)
		FROM post_views WHERE post_seq IN (<POST_LIST>) AND status='ACTIVE'
		GROUP BY post_seq
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SHARES_COUNTS
	</id>
	<stmt>
        SELECT count, post_seq FROM post_shares WHERE post_seq IN (<POST_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SEQ_LIKE_COUNT
	</id>
	<stmt>
        SELECT COUNT(like_seq) FROM post_likes WHERE post_seq=%s AND status='ACTIVE' AND type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SEQ_VIEW_COUNT
	</id>
	<stmt>
        SELECT COUNT(view_seq) FROM post_views WHERE post_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
        GET_COUNT_POST_LIKE
	</id>
	<stmt>
        SELECT COUNT(like_seq) FROM post_likes WHERE post_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_COMMENT_COUNTS
	</id>
	<stmt>
        SELECT COUNT(comment_seq), CAST(content_seq AS VARCHAR) FROM post_comments WHERE content_seq IN (<POST_LIST>) 
        AND content_type=%s AND status='ACTIVE' GROUP BY content_seq
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
        GET_POST_SEQ_COMMENT_COUNT
	</id>
	<stmt>
        SELECT COUNT(comment_seq) FROM post_comments WHERE content_seq=%s AND content_type=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_COUNT_POST_COMMENT
	</id>
	<stmt>
        SELECT COUNT(comment_seq) FROM post_comments WHERE content_seq=%s AND content_type=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_DETAILS
	</id>
	<stmt>
        SELECT user_profile.user_handle, user_profile.profile_picture, user_profile.cover_image, 
		user_profile.display_name, user_profile.account_holder, user_profile.account_type,
		user_profile.display_category, user_profile.status,
        user_profile.profile_bio, user_profile.type, user_profile.bank_account, user_profile.ifsc_code, 
		user_profile.fb_link, user_profile.twiter_link, user_profile.insta_link, user_profile.enable_comment, 
        user_profile.show_fan_count, user_profile.show_media_count, user_profile.paid_account_fee, 
		user_profile.is_verified, user_profile.is_verify_notified,
		user_master.country, user_master.state, user_master.email_id, user_profile.enable_email_alerts, 
		user_profile.enable_watermark, CAST(user_master.user_seq AS VARCHAR), user_master.gstin,
		CAST(user_profile.profile_seq AS VARCHAR), user_master.fcm_key, user_profile.s3_enabled,
		user_master.playlist_approved, user_profile.gender, user_master.ui_colour
        FROM user_profile, user_master WHERE user_profile.user_seq=user_master.user_seq AND user_profile.profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_FOR_USERS
	</id>
	<stmt>
        SELECT user_profile.user_handle, user_profile.profile_picture, user_profile.display_name, 
		CAST(user_profile.profile_seq AS VARCHAR), user_profile.cover_image, user_profile.enable_comment, 
		user_profile.is_verified, user_profile.type, user_profile.paid_account_fee, 
		user_profile.enable_watermark, user_profile.s3_enabled, user_master.gstin 
		FROM user_profile, user_master WHERE user_profile.user_seq=user_master.user_seq AND 
		user_profile.profile_seq IN (<PROFILE_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SAVE_POST_LIKE
	</id>
	<stmt>
        INSERT INTO post_likes (post_seq, liked_by_profile, like_time, type) VALUES (%s,%s,%s,%s) RETURNING 
		CAST(like_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SAVE_COMMENT_LIKE
	</id>
	<stmt>
        INSERT INTO post_comment_likes (comment_seq, post_seq, liked_by_profile, like_time) VALUES (%s,%s,%s,%s) 
		RETURNING CAST(like_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_LIKE_PROFILE
	</id>
	<stmt>
        SELECT CAST(like_seq AS VARCHAR) FROM post_likes WHERE post_seq=%s AND liked_by_profile=%s AND status='ACTIVE'
		AND type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_COMMENT_LIKE_PROFILE
	</id>
	<stmt>
        SELECT CAST(like_seq AS VARCHAR) FROM post_comment_likes WHERE comment_seq=%s AND liked_by_profile=%s 
		AND post_comment_likes.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        DELETE_POST_LIKE
	</id>
	<stmt>
        DELETE FROM post_likes WHERE liked_by_profile=%s AND post_seq=%s AND type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SAVE_COMMENT
	</id>
	<stmt>
        INSERT INTO post_comments (content_seq, content_type, comment, comment_profile_seq, comment_time, status) 
        VALUES (%s,%s,%s,%s,%s,%s) RETURNING CAST(comment_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        DELETE_COMMENT
	</id>
	<stmt>
        DELETE FROM post_comments WHERE comment_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        DELETE_COMMENT_LIKE
	</id>
	<stmt>
        DELETE FROM post_comment_likes WHERE liked_by_profile=%s AND comment_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_COMMENTS
	</id>
	<stmt>
        SELECT CAST(post_comments.comment_seq AS VARCHAR), post_comments.comment,
		CAST(post_comments.comment_time AS VARCHAR), CAST(post_comments.comment_profile_seq AS VARCHAR),
        user_profile.user_handle, CAST(post_comments.content_seq AS VARCHAR), 
		user_profile.display_name AS full_name, user_profile.profile_picture,
		user_profile.is_verified, user_profile.s3_enabled
        FROM post_comments, user_profile WHERE post_comments.comment_profile_seq=user_profile.profile_seq 
        AND post_comments.content_seq=%s AND post_comments.content_type=%s AND post_comments.status='ACTIVE' 
		ORDER BY post_comments.comment_time DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_COMMENTS_COUNT
	</id>
	<stmt>
        SELECT COUNT(comment_seq) FROM post_comments WHERE content_seq=%s AND content_type=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_COMMENT_COMMENTS
	</id>
	<stmt>
        SELECT CAST(post_comments.comment_seq AS VARCHAR), post_comments.comment, 
		CAST(post_comments.comment_time AS VARCHAR), CAST(post_comments.comment_profile_seq AS VARCHAR),
        user_profile.user_handle, CAST(post_comments.content_seq AS VARCHAR), 
		user_profile.display_name AS full_name, user_profile.profile_picture,
		user_profile.is_verified, user_profile.s3_enabled
        FROM post_comments, user_profile WHERE post_comments.comment_profile_seq=user_profile.profile_seq        
        AND post_comments.content_seq IN (<COMMENT_LIST>) AND post_comments.content_type=%s AND post_comments.status='ACTIVE'
		ORDER BY post_comments.comment_time DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_COMMENT_BY_PROFILE
	</id>
	<stmt>
        SELECT CAST(comment_seq AS VARCHAR) FROM post_comment_likes 
		WHERE liked_by_profile=%s AND comment_seq IN (<COMMENT_LIST>) AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_PROFILE_DETAILS
	</id>
	<stmt>
        UPDATE user_profile set user_handle=%s, display_name=%s, profile_bio=%s, display_category=%s, 
        profile_picture=%s, cover_image=%s, fb_link=%s, twiter_link=%s, insta_link=%s, s3_enabled=%s, gender=%s 
		WHERE profile_seq=%s        
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_LOCATION
	</id>
	<stmt>
        UPDATE user_master SET country=%s, state=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_DETAILS
	</id>
	<stmt>
        SELECT email_id, mobile_number, full_name, state, country, gstin, pan 
		FROM user_master WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_CONTACT
	</id>
	<stmt>
        UPDATE user_master SET mobile_number=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_STATE_COUNTRY
	</id>
	<stmt>
        UPDATE user_master SET country=%s, state=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_PROFILE_SETTING
	</id>
	<stmt>
        UPDATE user_profile SET type=%s, bank_account=%s, ifsc_code=%s, account_holder=%s, account_type=%s,
		paid_account_fee=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_FOLLOWER
	</id>
	<stmt>
        INSERT INTO profile_follower (follower_profile_seq, following_profile_seq, followed_on, status) VALUES (%s,%s,%s,%s) 
		RETURNING CAST(follower_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        DELETE_FOLLOWER
	</id>
	<stmt>
        DELETE FROM profile_follower WHERE follower_profile_seq=%s AND following_profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_FOLLOWERS
	</id>
	<stmt>
        SELECT CAST(profile_follower.follower_profile_seq AS VARCHAR), 
		user_profile.user_handle, user_profile.display_name, user_profile.profile_picture,
        user_profile.cover_image, user_profile.is_verified, user_profile.s3_enabled FROM profile_follower, user_profile 
        WHERE profile_follower.follower_profile_seq=user_profile.profile_seq
        AND profile_follower.following_profile_seq=%s AND user_profile.status='ACTIVE' AND profile_follower.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_FOLLOWER_COUNT
	</id>
	<stmt>
        SELECT COUNT(profile_follower.follower_profile_seq) FROM profile_follower, user_profile 
        WHERE profile_follower.follower_profile_seq=user_profile.profile_seq
        AND profile_follower.following_profile_seq=%s AND user_profile.status='ACTIVE' AND profile_follower.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_FOLLOWING
	</id>
	<stmt>
        SELECT CAST(profile_follower.following_profile_seq AS VARCHAR), 
		user_profile.user_handle, user_profile.display_name, user_profile.profile_picture,
        user_profile.cover_image, user_profile.is_verified, user_profile.s3_enabled FROM profile_follower, user_profile 
        WHERE profile_follower.following_profile_seq=user_profile.profile_seq
        AND profile_follower.follower_profile_seq=%s AND user_profile.status='ACTIVE' AND profile_follower.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_FOLLOWING_COUNT
	</id>
	<stmt>
        SELECT COUNT(profile_follower.following_profile_seq) FROM profile_follower, user_profile 
        WHERE profile_follower.following_profile_seq=user_profile.profile_seq
        AND profile_follower.follower_profile_seq=%s AND user_profile.status='ACTIVE' AND profile_follower.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_BOOKMARK
	</id>
	<stmt>
        INSERT INTO profile_bookmarks (post_seq, profile_seq, bookmarked_on, status) VALUES (%s,%s,%s,%s) 
		RETURNING CAST(bookmark_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        DELETE_BOOKMARK
	</id>
	<stmt>
        DELETE FROM profile_bookmarks WHERE post_seq=%s AND profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_POSTS
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), CAST(user_posts.profile_seq AS VARCHAR), 
		user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee, 
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, user_post_content.media_file, 
		user_post_content.media_type, user_post_content.media_cover,
		user_posts.block_reason, user_post_content.video_duration, 
		CAST(user_post_content.content_seq AS VARCHAR), user_post_content.fuzzy_image,
		user_post_content.s3_enabled
        FROM user_posts, user_post_content, user_profile WHERE user_posts.post_seq=user_post_content.post_seq AND 
		user_posts.profile_seq=user_profile.profile_seq AND user_profile.status='ACTIVE' AND user_posts.is_playlist='NO' AND
		user_posts.profile_seq=%s AND user_posts.status IN (<STATUS_LIST>)
        AND user_post_content.media_type IN (<MEDIA_TYPE_LIST>) AND user_posts.expire_on>=%s AND 
		user_posts.post_seq NOT IN (SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='POST')
		ORDER BY user_posts.posted_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_POSTS_SPECIAL
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), CAST(user_posts.profile_seq AS VARCHAR), 
		user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee, 
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, user_post_content.media_file, 
		user_post_content.media_type, user_post_content.media_cover,
		user_posts.block_reason, user_post_content.video_duration, 
		CAST(user_post_content.content_seq AS VARCHAR), user_post_content.fuzzy_image,
		user_post_content.s3_enabled
        FROM user_posts, user_post_content, user_profile WHERE user_posts.post_seq=user_post_content.post_seq AND 
		user_posts.profile_seq=user_profile.profile_seq AND
		user_posts.post_seq=%s 
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_POSTS_COUNT
	</id>
	<stmt>
        SELECT COUNT(user_posts.post_seq) FROM user_posts, user_post_content, user_profile
		WHERE user_posts.post_seq=user_post_content.post_seq AND user_posts.profile_seq=user_profile.profile_seq AND
        user_posts.profile_seq=%s AND user_posts.status IN (<STATUS_LIST>) AND user_post_content.media_type IN (<MEDIA_TYPE_LIST>)
		AND user_posts.expire_on>=%s AND user_profile.status='ACTIVE' AND user_posts.post_seq NOT IN 
		(SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='POST')
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_BOOKMARKS
	</id>
	<stmt>
        SELECT CAST(profile_bookmarks.bookmark_seq AS VARCHAR), 
		CAST(profile_bookmarks.post_seq AS VARCHAR), user_post_content.media_file,
        user_post_content.media_type, user_post_content.media_format, 
		CAST(user_posts.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.display_name,
        user_profile.profile_picture, user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee,
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, user_post_content.media_cover, 
		user_profile.is_verified, user_profile.type, user_profile.s3_enabled AS s3_profile, 
		user_post_content.s3_enabled AS s3_post
        FROM profile_bookmarks, user_post_content, user_posts, user_profile
        WHERE profile_bookmarks.post_seq=user_post_content.post_seq AND profile_bookmarks.post_seq=user_posts.post_seq AND
        user_posts.profile_seq=user_profile.profile_seq AND profile_bookmarks.profile_seq=%s AND profile_bookmarks.status='ACTIVE' 
        AND user_profile.status='ACTIVE' AND user_posts.status='ACTIVE' AND user_posts.expire_on>=%s AND 
		user_posts.post_seq NOT IN (SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='POST') AND user_profile.profile_seq NOT IN
		(SELECT restricted_profile FROM restricted_profiles WHERE restricted_by=%s AND status='ACTIVE' AND restriction_type='BLOCKED')
		<RESTRICTED_OTHERS> ORDER BY profile_bookmarks.bookmarked_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_BOOKMARK_COUNT
	</id>
	<stmt>
        SELECT COUNT(profile_bookmarks.bookmark_seq) FROM profile_bookmarks, user_post_content, user_posts, user_profile
	WHERE profile_bookmarks.post_seq=user_post_content.post_seq AND profile_bookmarks.post_seq=user_posts.post_seq AND
	user_posts.profile_seq=user_profile.profile_seq AND profile_bookmarks.profile_seq=%s AND profile_bookmarks.status='ACTIVE' 
	AND user_profile.status='ACTIVE' AND user_posts.status='ACTIVE' AND user_posts.expire_on>=%s AND 
		user_posts.post_seq NOT IN (SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='POST') AND user_profile.profile_seq NOT IN
		(SELECT restricted_profile FROM restricted_profiles WHERE restricted_by=%s AND status='ACTIVE' AND restriction_type='BLOCKED')
		<RESTRICTED_OTHERS>
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_FOLLOWING
	</id>
	<stmt>
        SELECT CAST(follower_seq AS VARCHAR) FROM profile_follower 
		WHERE follower_profile_seq=%s AND following_profile_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SORTED_ON_FOLLOWERS
	</id>
	<stmt>
        SELECT COUNT(profile_follower.follower_seq), CAST(profile_follower.following_profile_seq AS VARCHAR)
		FROM profile_follower, user_profile WHERE profile_follower.following_profile_seq=user_profile.profile_seq AND
		profile_follower.status='ACTIVE' AND user_profile.status='ACTIVE' AND
        profile_follower.following_profile_seq NOT IN (SELECT following_profile_seq FROM profile_follower WHERE status='ACTIVE' AND follower_profile_seq=%s) AND
        profile_follower.following_profile_seq!=%s AND profile_follower.following_profile_seq NOT IN 
		(SELECT restricted_profile FROM restricted_profiles WHERE restricted_by=%s AND status='ACTIVE' AND restriction_type='BLOCKED')
		<RESTRICTED_OTHERS>
		GROUP BY profile_follower.following_profile_seq ORDER BY count DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SORTED_ON_FOLLOWERS_COUNT
	</id>
	<stmt>
        SELECT COUNT(profile_follower.follower_seq), CAST(profile_follower.following_profile_seq AS VARCHAR)
		FROM profile_follower, user_profile WHERE profile_follower.following_profile_seq=user_profile.profile_seq AND
		profile_follower.status='ACTIVE' AND user_profile.status='ACTIVE' AND
        profile_follower.following_profile_seq NOT IN (SELECT following_profile_seq FROM profile_follower WHERE status='ACTIVE' AND follower_profile_seq=%s) AND
        profile_follower.following_profile_seq!=%s AND profile_follower.following_profile_seq NOT IN 
		(SELECT restricted_profile FROM restricted_profiles WHERE restricted_by=%s AND status='ACTIVE' AND restriction_type='BLOCKED')
		<RESTRICTED_OTHERS>
		GROUP BY profile_follower.following_profile_seq ORDER BY count DESC LIMIT %s OFFSET 0
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_STORY
	</id>
	<stmt>
        INSERT INTO user_stories (profile_seq, story_comments, posted_on, expire_on, media_type, media_size, media_format,
		media_file, status, media_cover, video_duration, s3_enabled)
        VALUES(%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(story_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_STORY_LIST
	</id>
	<stmt>
        SELECT CAST(user_stories.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.display_name, 
		user_profile.profile_picture, user_profile.is_verified, user_profile.s3_enabled,
		CAST(user_stories.story_seq AS VARCHAR)
        FROM user_stories, user_profile WHERE user_stories.profile_seq=user_profile.profile_seq AND user_stories.status='ACTIVE' AND
        user_stories.posted_on>=%s AND user_stories.profile_seq != %s AND user_stories.story_seq NOT IN 
		(SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='STORY')
		ORDER BY user_stories.posted_on DESC LIMIT %s OFFSET 0
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_STORY_LIST_USER
	</id>
	<stmt>
        SELECT CAST(user_stories.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.display_name, 
		user_profile.profile_picture,
        user_stories.media_file, user_stories.media_type, user_stories.media_format, 
		user_stories.story_comments, CAST(user_stories.story_seq AS VARCHAR), 
        user_stories.media_cover, user_profile.is_verified, user_stories.video_duration, user_stories.status,
		user_stories.block_reason, user_profile.s3_enabled AS s3_profile, user_stories.s3_enabled AS s3_story
        FROM user_stories, user_profile WHERE user_stories.profile_seq=user_profile.profile_seq AND 
        user_stories.story_seq IN (<STORY_LIST>)	
		ORDER BY user_stories.posted_on ASC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_STORY_USER
	</id>
	<stmt>
        SELECT CAST(user_stories.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.display_name, 
		user_profile.profile_picture,
        user_stories.media_file, user_stories.media_type, user_stories.media_format, 
		user_stories.story_comments, CAST(user_stories.story_seq AS VARCHAR), 
        user_stories.media_cover, user_profile.is_verified, user_stories.video_duration, user_stories.status,
		user_stories.block_reason, user_profile.s3_enabled AS s3_profile, user_stories.s3_enabled AS s3_story
        FROM user_stories, user_profile WHERE user_stories.profile_seq=user_profile.profile_seq AND user_stories.status <STATUS_QUERY> AND
        user_stories.posted_on>=%s AND user_stories.profile_seq=%s AND user_stories.story_seq NOT IN 
		(SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='STORY')	
		ORDER BY user_stories.posted_on ASC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_STORY_USER_COUNT
	</id>
	<stmt>
        SELECT COUNT(story_seq) FROM user_stories WHERE status='ACTIVE' AND posted_on>=%s AND profile_seq=%s AND 
		user_stories.story_seq NOT IN (SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='STORY')
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_STORY_COUNT
	</id>
	<stmt>
        SELECT COUNT(story_seq), CASt(profile_seq AS VARCHAR) FROM user_stories 
		WHERE status='ACTIVE' AND posted_on>=%s AND profile_seq IN (<PROFILE_LIST>)
        GROUP BY profile_seq
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_STORY_STATUS
	</id>
	<stmt>
        UPDATE user_stories SET status=%s WHERE story_seq=%s AND profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_PRIVACY
	</id>
	<stmt>
        UPDATE user_profile SET show_fan_count=%s, show_media_count=%s, enable_comment=%s, enable_watermark=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PRIVACY
	</id>
	<stmt>
        SELECT show_fan_count, show_media_count, enable_comment, enable_watermark FROM user_profile WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_SESSION_CREDS
	</id>
	<stmt>
        INSERT INTO session_credentials (key,uid,passwd) VALUES(%s,%s,%s) RETURNING CAST(credential_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SESSION_CREDS
	</id>
	<stmt>
        SELECT key FROM session_credentials WHERE uid=%s AND passwd=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_MEDIA_COUNT
	</id>
	<stmt>
        SELECT COUNT(post_seq) FROM user_posts WHERE profile_seq=%s AND status='ACTIVE' 
		AND expire_on>=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_FREE_MEDIA_COUNT
	</id>
	<stmt>
        SELECT COUNT(post_seq) FROM user_posts WHERE profile_seq=%s AND status='ACTIVE' AND post_type='FREE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_PROFILES
	</id>
	<stmt>
        SELECT DISTINCT ON (user_profile.profile_seq) CAST(user_profile.profile_seq AS VARCHAR)
		FROM user_profile
        WHERE		
		(user_profile.user_handle ILIKE <USER_HANDLE> OR user_profile.display_name ILIKE <DISP_NAME>) 
		AND user_profile.status='ACTIVE' AND user_profile.profile_seq!=<SKIP_PROFILE>
		AND user_profile.is_verified='YES'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_PROFILES_INTERESTS
	</id>
	<stmt>
        SELECT DISTINCT ON (user_profile.profile_seq) CAST(user_profile.profile_seq AS VARCHAR)
		FROM user_profile, user_interests
        WHERE user_profile.user_seq=user_interests.user_seq AND
		user_interests.interest_name ILIKE <CATEGORY_LIST> AND user_interests.status='ACTIVE'
		AND user_profile.status='ACTIVE' AND user_profile.profile_seq!=<SKIP_PROFILE>
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_PROFILES_DEFAULT
	</id>
	<stmt>
        SELECT DISTINCT ON (user_profile.profile_seq) CAST(user_profile.profile_seq AS VARCHAR)
		FROM user_profile, profile_category
        WHERE user_profile.profile_seq=profile_category.profile_seq		 
		AND user_profile.status='ACTIVE' AND user_profile.profile_seq!=<SKIP_PROFILE>
		AND user_profile.is_verified='YES'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        COUNT_SEARCH_PROFILES_DEFAULT
	</id>
	<stmt>
        SELECT COUNT(DISTINCT(user_profile.profile_seq))
		FROM user_profile, profile_category
        WHERE user_profile.profile_seq=profile_category.profile_seq				 
		AND user_profile.status='ACTIVE' AND user_profile.profile_seq!=<SKIP_PROFILE>
		AND user_profile.is_verified='YES'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
        SEARCH_PROFILES_DATA
	</id>
	<stmt>
        SELECT CAST(user_profile.profile_seq AS VARCHAR), user_profile.user_handle, 
		user_profile.profile_picture, user_profile.display_name, 
		user_profile.is_verified, user_profile.s3_enabled 
		FROM user_profile WHERE user_profile.profile_seq IN (<PROFILE_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        COUNT_SEARCH_PROFILES
	</id>
	<stmt>		
		SELECT COUNT(DISTINCT(user_profile.profile_seq))
		FROM user_profile
        WHERE		
		(user_profile.user_handle ILIKE <USER_HANDLE> OR user_profile.display_name ILIKE <DISP_NAME>) 
		AND user_profile.status='ACTIVE' AND user_profile.profile_seq!=<SKIP_PROFILE>
		AND user_profile.is_verified='YES'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        COUNT_SEARCH_PROFILES_INTERESTS
	</id>
	<stmt>
		SELECT COUNT(DISTINCT(user_profile.profile_seq))
		FROM user_profile, user_interests
        WHERE user_profile.user_seq=user_interests.user_seq AND
		user_interests.interest_name ILIKE <CATEGORY_LIST> AND user_interests.status='ACTIVE'
		AND user_profile.status='ACTIVE' AND user_profile.profile_seq!=<SKIP_PROFILE>
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_PLAYLIST
	</id>
	<stmt>
        SELECT DISTINCT(show_master.show_seq) AS show_seq
		FROM show_master LEFT JOIN show_topics ON show_master.show_seq=show_topics.show_seq WHERE		
		show_master.status='ACTIVE' AND show_master.approval_status='APPROVED' AND 
		(show_master.title ILIKE <TITLE> OR show_master.description ILIKE <DESCRIPTION> or show_topics.topic ILIKE <TOPIC>) AND
		show_master.expiry_date > <EXP_DATE> AND show_master.scheduled_on <= <SCHED_DATE> AND
		show_master.user_seq != <USER_SEQ> ORDER BY show_master.show_seq DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_PLAYLIST_DETAILS
	</id>
	<stmt>
        SELECT show_master.show_seq, show_master.title, show_master.thumb_file, show_master.logo_file, 
		show_master.description, show_master.is_paid, show_master.price, show_master.s3_enabled, grid_title
		FROM show_master WHERE show_master.show_seq IN (<SHOW_SEQ_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        COUNT_SEARCH_PLAYLIST
	</id>
	<stmt>
		SELECT COUNT(show_master.show_seq)
		FROM show_master LEFT JOIN show_topics ON show_master.show_seq=show_topics.show_seq WHERE		
		show_master.status='ACTIVE' AND show_master.approval_status='APPROVED' AND 
		(show_master.title ILIKE <TITLE> OR show_master.description ILIKE <DESCRIPTION> or show_topics.topic ILIKE <TOPIC>) AND
		show_master.expiry_date > <EXP_DATE> AND show_master.scheduled_on <= <SCHED_DATE>
		AND show_master.user_seq != <USER_SEQ>
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_PLAYLIST_DEFAULT
	</id>
	<stmt>
        SELECT show_master.show_seq, show_master.title, show_master.thumb_file, show_master.logo_file, 
		show_master.description, show_master.is_paid, show_master.price, show_master.s3_enabled, grid_title
		FROM show_master WHERE		
		show_master.status='ACTIVE' AND show_master.approval_status='APPROVED' AND 
		show_master.expiry_date>%s AND show_master.scheduled_on<=%s ORDER BY show_master.show_seq DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        COUNT_SEARCH_PLAYLIST_DEFAULT
	</id>
	<stmt>
        SELECT COUNT(show_master.show_seq) FROM show_master WHERE		
		show_master.status='ACTIVE' AND show_master.approval_status='APPROVED' AND 
		show_master.expiry_date>%s AND show_master.scheduled_on<=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_TOPIC
	</id>
	<stmt>
        SELECT DISTINCT(user_posts.post_seq) AS post_seq
		FROM user_posts, user_post_content, user_profile, post_topics 
		WHERE user_posts.post_seq=user_post_content.post_seq AND
		user_posts.profile_seq=user_profile.profile_seq AND user_posts.post_seq=post_topics.post_seq 
		AND user_posts.is_playlist='NO' AND post_topics.topic ILIKE <TOPIC> AND 
		(user_posts.expire_on IS NULL OR user_posts.expire_on > <EXPIRE_DATE>) AND
		user_posts.profile_seq!=<PROFILE_SEQ> AND user_posts.status='ACTIVE' AND 
		user_profile.status='ACTIVE' AND user_profile.is_verified='YES'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SEARCH_TOPIC_DETAILS
	</id>
	<stmt>
        SELECT user_posts.post_seq, user_posts.profile_seq, user_posts.post_type, user_post_content.media_file,
		user_post_content.media_type, user_post_content.media_cover, user_post_content.fuzzy_image, 
		user_profile.type AS profile_type, user_post_content.s3_enabled
		FROM user_posts, user_post_content, user_profile
		WHERE user_posts.post_seq=user_post_content.post_seq AND user_posts.profile_seq=user_profile.profile_seq		
		AND user_posts.post_seq IN (<POST_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        COUNT_SEARCH_TOPIC
	</id>
	<stmt>
        SELECT COUNT(DISTINCT(user_posts.post_seq))
		FROM user_posts, user_post_content, user_profile, post_topics 
		WHERE user_posts.post_seq=user_post_content.post_seq AND
		user_posts.profile_seq=user_profile.profile_seq AND user_posts.post_seq=post_topics.post_seq 
		AND user_posts.is_playlist='NO' AND post_topics.topic ILIKE <TOPIC> AND 
		(user_posts.expire_on IS NULL OR user_posts.expire_on > <EXPIRE_DATE>)
		AND user_posts.profile_seq!=<PROFILE_SEQ> AND user_posts.status='ACTIVE' AND 
		user_profile.status='ACTIVE' AND user_profile.is_verified='YES'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_TOPIC_DEFAULT
	</id>
	<stmt>
        SELECT user_posts.post_seq, user_posts.profile_seq, user_posts.post_type, user_post_content.media_file,
		user_post_content.media_type, user_post_content.media_cover, user_post_content.fuzzy_image, user_profile.type AS profile_type,
		user_post_content.s3_enabled
		FROM user_posts, user_post_content, user_profile 
		WHERE user_posts.post_seq=user_post_content.post_seq AND
		user_posts.profile_seq=user_profile.profile_seq
		AND user_posts.is_playlist='NO' AND 
		(user_posts.expire_on IS NULL OR user_posts.expire_on > %s) AND user_posts.status='ACTIVE' AND 
		user_profile.status='ACTIVE' AND user_profile.is_verified='YES' ORDER BY user_posts.post_seq DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        COUNT_SEARCH_TOPIC_DEFAULT
	</id>
	<stmt>
        SELECT COUNT(user_posts.post_seq) FROM user_posts, user_post_content, user_profile 
		WHERE user_posts.post_seq=user_post_content.post_seq AND
		user_posts.profile_seq=user_profile.profile_seq 
		AND user_posts.is_playlist='NO' AND 
		(user_posts.expire_on IS NULL OR user_posts.expire_on > %s) AND user_posts.status='ACTIVE' AND 
		user_profile.status='ACTIVE' AND user_profile.is_verified='YES'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_PROFILES_TYPEAHEAD
	</id>
	<stmt>
        SELECT CAST(profile_seq, user_handle AS VARCHAR), display_name FROM user_profile 
        WHERE user_handle ILIKE <USER_HANDLE> OR display_name ILIKE <DISP_NAME> LIMIT 100 OFFSET 0 
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_PAYMENT_INIT
	</id>
	<stmt>
        INSERT INTO gateway_payment_init (init_time, profile_seq, user_seq, amount, status, gateway_id, transaction_id)         
        VALUES (%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(init_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_PAYMENT_INIT_HASH
	</id>
	<stmt>
        INSERT INTO gateway_payment_init (init_time, profile_seq, user_seq, amount, status, gateway_id, 
		transaction_id,redirect_url,subscribe_to,subscribe_seq) VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) 
		RETURNING CAST(init_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_PAYMENT_STATUS
	</id>
	<stmt>
        INSERT INTO gateway_payment_status (init_seq,status,amount,raw_response,response_ts)         
        VALUES (%s,%s,%s,%s,%s) RETURNING CAST(status_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENT_INIT
	</id>
	<stmt>
        SELECT amount, status, gateway_id FROM gateway_payment_init 
		WHERE init_seq=%s AND profile_seq=%s AND user_seq=%s AND gateway_id=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENT_INIT_HASH
	</id>
	<stmt>
        SELECT amount, status, CAST(init_seq AS VARCHAR), CAST(profile_seq AS VARCHAR), 
		CAST(user_seq AS VARCHAR), redirect_url, subscribe_to, CAST(subscribe_seq AS VARCHAR) 
		FROM gateway_payment_init WHERE transaction_id=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENT_INIT_DATA
	</id>
	<stmt>
        SELECT amount, status, CAST(profile_seq AS VARCHAR), 
		CAST(user_seq AS VARCHAR), CAST(subscribe_to AS VARCHAR), CAST(subscribe_seq AS VARCHAR)
		FROM gateway_payment_init WHERE init_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_INIT_STATUS
	</id>
	<stmt>
        UPDATE gateway_payment_init SET status=%s WHERE init_seq=%s AND gateway_id=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_INIT_STATUS_HASH
	</id>
	<stmt>
        UPDATE gateway_payment_init SET status=%s, gateway_id=%s WHERE init_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_BALANCE
	</id>
	<stmt>
        SELECT profile_balance FROM user_profile WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_PROFILE_BALANCE
	</id>
	<stmt>
        UPDATE user_profile SET profile_balance=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_PAYMENT_RECORD
	</id>
	<stmt>
        INSERT INTO profile_payments (profile_seq, user_seq, paid_amount, paid_on, payment_status_seq) 
        VALUES (%s,%s,%s,%s,%s) RETURNING CAST(payment_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_PROFILE_SUBSCRIPTION
	</id>
	<stmt>
        INSERT INTO profile_subscription (subscribed_by_seq, subscribed_to_seq, charge_seq, from_date, to_date, status) 
        VALUES (%s,%s,%s,%s,%s,%s) RETURNING CAST(subscription_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_CHARGE_RECORD
	</id>
	<stmt>
        INSERT INTO profile_charges (profile_seq, user_seq, paid_amount, paid_on, paid_for, paid_key, gst, inward_charges,
		outward_charges, comission, gst_comission, tcs, tds, payable) 
        VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(charge_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_POST_SUBSCRIPTION
	</id>
	<stmt>
        INSERT INTO post_subscription (subscribed_by_seq,subscribed_post_seq,charge_seq,status,subscribed_on,subscribed_to_seq) 
        VALUES (%s,%s,%s,%s,%s,%s) RETURNING CAST(subscription_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_PROFILE_SUBSCRIPION
	</id>
	<stmt>
        SELECT CAST(subscription_seq AS VARCHAR) FROM profile_subscription WHERE 
		subscribed_by_seq=%s AND subscribed_to_seq=%s AND from_date<=%s AND to_date>=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_POST_SUBSCRIPION
	</id>
	<stmt>
        SELECT CAST(subscription_seq AS VARCHAR) FROM post_subscription 
		WHERE subscribed_by_seq=%s AND subscribed_post_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_POST_SUBSCRIPION_LIST
	</id>
	<stmt>
        SELECT CAST(subscription_seq AS VARCHAR), CAST(subscribed_post_seq AS VARCHAR) 
		FROM post_subscription WHERE subscribed_by_seq=%s AND subscribed_post_seq IN (<POST_LIST>)
		AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIPTIONS
	</id>
	<stmt>
        SELECT CAST(profile_subscription.subscription_seq AS VARCHAR), 
		CAST(profile_subscription.subscribed_to_seq AS VARCHAR), CAST(profile_subscription.from_date AS VARCHAR), 
        CAST(profile_subscription.to_date AS VARCHAR), user_profile.user_handle, 
		user_profile.display_name, user_profile.profile_picture,
        user_profile.cover_image, user_profile.is_verified, user_profile.s3_enabled
        FROM profile_subscription, user_profile 
        WHERE profile_subscription.subscribed_to_seq=user_profile.profile_seq AND profile_subscription.status='ACTIVE' 
        AND profile_subscription.subscribed_by_seq=%s AND profile_subscription.from_date<=%s AND profile_subscription.to_date>=%s
        ORDER BY profile_subscription.from_date DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIPTIONS_PAST
	</id>
	<stmt>
        SELECT CAST(profile_subscription.subscription_seq AS VARCHAR), 
		CAST(profile_subscription.subscribed_to_seq AS VARCHAR), CAST(profile_subscription.from_date AS VARCHAR), 
        CAST(profile_subscription.to_date AS VARCHAR), user_profile.user_handle, user_profile.display_name, 
		user_profile.profile_picture, user_profile.cover_image, user_profile.is_verified, user_profile.s3_enabled
        FROM profile_subscription, user_profile 
        WHERE profile_subscription.subscribed_to_seq=user_profile.profile_seq AND profile_subscription.status='ACTIVE' 
        AND profile_subscription.subscribed_by_seq=%s AND profile_subscription.to_date<%s
        ORDER BY profile_subscription.from_date DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIPTIONS_COUNT
	</id>
	<stmt>
        SELECT COUNT(subscription_seq) FROM profile_subscription WHERE  status='ACTIVE' AND subscribed_by_seq=%s
        AND profile_subscription.from_date<=%s AND profile_subscription.to_date>=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIBER_COUNT
	</id>
	<stmt>
        SELECT COUNT(subscription_seq) FROM profile_subscription WHERE  status='ACTIVE' AND subscribed_to_seq=%s
        AND profile_subscription.from_date<=%s AND profile_subscription.to_date>=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIPTIONS_PAST_COUNT
	</id>
	<stmt>
        SELECT COUNT(subscription_seq) FROM profile_subscription WHERE  status='ACTIVE' AND subscribed_by_seq=%s
        AND profile_subscription.to_date<%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SUBSCRIPTIONS
	</id>
	<stmt>
        SELECT CAST(post_subscription.subscription_seq AS VARCHAR), CAST(post_subscription.subscribed_on AS VARCHAR),
        user_profile.user_handle, user_profile.display_name, user_profile.profile_picture, user_post_content.media_file, 
        user_post_content.media_type,user_posts.post_comments,user_posts.post_type, CAST(user_posts.posted_on AS VARCHAR),
        user_post_content.media_format, user_post_content.media_cover, 
		CAST(user_post_content.post_seq AS VARCHAR), CAST(user_profile.profile_seq AS VARCHAR),
		user_profile.is_verified, user_profile.s3_enabled AS s3_profile, user_post_content.s3_enabled AS s3_post
        FROM post_subscription, user_profile, user_post_content, user_posts 
        WHERE post_subscription.subscribed_to_seq=user_profile.profile_seq AND post_subscription.subscribed_post_seq=user_post_content.post_seq AND
        post_subscription.subscribed_post_seq=user_posts.post_seq AND user_posts.status='ACTIVE' AND 
        post_subscription.subscribed_by_seq=%s AND post_subscription.status='ACTIVE' AND user_posts.expire_on>=%s AND user_profile.status='ACTIVE' AND
		user_posts.post_seq NOT IN (SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='POST') AND post_subscription.subscribed_to_seq NOT IN
		(SELECT restricted_profile FROM restricted_profiles WHERE restricted_by=%s AND status='ACTIVE' AND restriction_type='BLOCKED')
		<RESTRICTED_OTHERS>
		ORDER BY post_subscription.subscribed_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SUBSCRIPTIONS_COUNT
	</id>
	<stmt>
        SELECT COUNT(post_subscription.subscription_seq)
        FROM post_subscription, user_profile, user_post_content, user_posts 
        WHERE post_subscription.subscribed_to_seq=user_profile.profile_seq AND post_subscription.subscribed_post_seq=user_post_content.post_seq AND
        post_subscription.subscribed_post_seq=user_posts.post_seq AND user_posts.status='ACTIVE' AND
        post_subscription.subscribed_by_seq=%s AND post_subscription.status='ACTIVE' AND user_posts.expire_on>=%s AND user_profile.status='ACTIVE' AND
		user_posts.post_seq NOT IN (SELECT post_seq FROM post_reports WHERE reported_by=%s AND report_source='POST') AND post_subscription.subscribed_to_seq NOT IN
		(SELECT restricted_profile FROM restricted_profiles WHERE restricted_by=%s AND status='ACTIVE' AND restriction_type='BLOCKED')
		<RESTRICTED_OTHERS>
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_PROFILE_SUBSCRIPTION
	</id>
	<stmt>
        UPDATE profile_subscription SET status=%s, unsubscribe_on=%s WHERE subscription_seq=%s        
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENTS_CREDIT
	</id>
	<stmt>
        SELECT CAST(payment_seq AS VARCHAR), paid_amount, CAST(paid_on AS VARCHAR) 
		FROM profile_payments WHERE user_seq=%s AND profile_seq=%s ORDER BY paid_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENTS_CREDIT_COUNT
	</id>
	<stmt>
        SELECT COUNT(payment_seq) FROM profile_payments WHERE user_seq=%s AND profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENTS_DEBIT
	</id>
	<stmt>
        SELECT CAST(charge_seq AS VARCHAR), paid_amount, CAST(paid_on AS VARCHAR), paid_for, CAST(paid_key AS VARCHAR)
		FROM profile_charges WHERE user_seq=%s AND profile_seq=%s ORDER BY paid_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENTS_DEBIT_COUNT
	</id>
	<stmt>
        SELECT COUNT(charge_seq) FROM profile_charges WHERE user_seq=%s AND profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_USER_DETAILS
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), CAST(user_posts.profile_seq AS VARCHAR), 
		user_profile.display_name FROM user_posts, user_profile WHERE 
        user_posts.profile_seq=user_profile.profile_seq AND post_seq IN (<POST_LIST>) AND user_posts.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SUBSCRIBERS
	</id>
	<stmt>
        SELECT CAST(post_subscription.subscribed_by_seq AS VARCHAR), user_profile.display_name, 
		user_profile.user_handle, user_profile.is_verified, 
        CAST(post_subscription.subscribed_on AS VARCHAR), profile_charges.paid_amount
        FROM post_subscription, user_profile, profile_charges WHERE post_subscription.subscribed_by_seq=user_profile.profile_seq AND
        post_subscription.charge_seq=profile_charges.charge_seq AND post_subscription.subscribed_to_seq=%s 
		AND post_subscription.status='ACTIVE' ORDER BY post_subscription.subscribed_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SUBSCRIBERS_COUNT
	</id>
	<stmt>
        SELECT COUNT(subscribed_by_seq) FROM post_subscription WHERE subscribed_to_seq=%s AND status='ACTIVE'       
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIBERS
	</id>
	<stmt>
        SELECT CAST(profile_subscription.subscribed_by_seq AS VARCHAR), user_profile.display_name, 
		user_profile.user_handle, 
        CAST(profile_subscription.from_date AS VARCHAR), CAST(profile_subscription.to_date AS VARCHAR), 
		profile_charges.paid_amount,
        user_profile.profile_picture,user_profile.cover_image,user_profile.is_verified
        FROM profile_subscription, user_profile, profile_charges WHERE profile_subscription.subscribed_by_seq=user_profile.profile_seq AND
        profile_subscription.charge_seq=profile_charges.charge_seq AND profile_subscription.subscribed_to_seq=%s AND profile_subscription.status='ACTIVE'
        AND profile_subscription.from_date<=%s AND profile_subscription.to_date>=%s
        ORDER BY profile_subscription.from_date DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIBERS_COUNT
	</id>
	<stmt>
        SELECT COUNT(subscribed_by_seq) FROM profile_subscription WHERE subscribed_to_seq=%s AND profile_subscription.status='ACTIVE' 
        AND profile_subscription.from_date<=%s AND profile_subscription.to_date>=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIBERS_PAID
	</id>
	<stmt>
        SELECT CAST(profile_subscription.subscribed_by_seq AS VARCHAR), user_profile.display_name, 
		user_profile.user_handle, user_profile.is_verified,
        CAST(profile_subscription.from_date AS VARCHAR), CAST(profile_subscription.to_date AS VARCHAR), 
		profile_charges.paid_amount,
        profile_subscription.status, user_profile.profile_picture, user_profile.cover_image, user_profile.s3_enabled
        FROM profile_subscription, user_profile, profile_charges WHERE profile_subscription.subscribed_by_seq=user_profile.profile_seq AND
        profile_subscription.charge_seq=profile_charges.charge_seq AND profile_subscription.subscribed_to_seq=%s AND
		profile_subscription.status LIKE %s ORDER BY profile_subscription.from_date DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIBERS_PAID_COUNT
	</id>
	<stmt>
        SELECT COUNT(subscribed_by_seq) FROM profile_subscription WHERE subscribed_to_seq=%s AND status LIKE %s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_REPORT_POST
	</id>
	<stmt>
        INSERT INTO post_reports (post_seq, reason_code, comments, reported_by, reported_on, status,
		link_existing,link_stolen,screen_capture,report_id,report_source,s3_enabled) 
		VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(report_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_REPORT_ID
	</id>
	<stmt>
        UPDATE post_reports SET report_id=%s WHERE report_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_PROFILE_STATUS
	</id>
	<stmt>
        UPDATE user_profile SET status=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_PROFILE_VERIFICATION
	</id>
	<stmt>
        INSERT INTO profile_verification (profile_seq, verification_picture, document_image, combined_image,
		submitted_on, verified_status, type, s3_enabled, document_type, playlist_applied) 
		VALUES (%s,%s,%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(verification_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_PROFILE_VERIFICATION_FIRM
	</id>
	<stmt>
        INSERT INTO profile_verification (profile_seq, submitted_on, verified_status, type, gstin, pan, playlist_applied) 
		VALUES (%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(verification_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_VERIFICATION_DETAILS
	</id>
	<stmt>
        SELECT CAST(verification_seq AS VARCHAR), verification_picture, document_image, submitted_on, 
		verified_status FROM profile_verification 
		WHERE profile_seq=%s ORDER BY verification_seq DESC LIMIT 1 OFFSET 0
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_VERIFICATION
	</id>
	<stmt>
        SELECT CAST(verification_seq AS VARCHAR), verification_picture, document_image, submitted_on, 
		verified_status, verification_comments FROM 
		profile_verification WHERE profile_seq=%s ORDER BY verification_seq DESC LIMIT 1 OFFSET 0
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_VERIFICATION
	</id>
	<stmt>
        SELECT CAST(verification_seq AS VARCHAR), verification_picture, document_image, submitted_on, 
		verified_status, verification_comments FROM 
		profile_verification WHERE profile_seq=%s ORDER BY verification_seq DESC LIMIT 1 OFFSET 0
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       INSERT_RESTRICTED_PROFILE
	</id>
	<stmt>
        INSERT INTO restricted_profiles (restricted_profile, restricted_by, restricted_on, reason_code, reason, 
		restriction_type,status) VALUES (%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(restrict_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       CHECK_RESTRICTED_PROFILE
	</id>
	<stmt>
        SELECT CAST(restrict_seq AS VARCHAR) FROM restricted_profiles WHERE restricted_by=%s 
		AND restricted_profile=%s AND restriction_type=%s AND status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       CHECK_RESTRICTED_PROFILE_TYPE
	</id>
	<stmt>
        SELECT CAST(restrict_seq AS VARCHAR), restriction_type FROM restricted_profiles 
		WHERE restricted_by=%s AND restricted_profile=%s AND status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       UPDATE_RESTRICTED_PROFILE
	</id>
	<stmt>
        UPDATE restricted_profiles SET restriction_type=%s, restricted_on=%s, reason_code=%s, reason=%s 
		WHERE restrict_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_RESTRICTED_PROFILES
	</id>
	<stmt>
         SELECT CAST(restricted_profiles.restrict_seq AS VARCHAR), 
		 CAST(restricted_profiles.restricted_profile AS VARCHAR), 
		 CAST(restricted_profiles.restricted_on AS VARCHAR), 
		restricted_profiles.reason_code, restricted_profiles.reason, user_profile.user_handle, 
		user_profile.display_name, user_profile.profile_picture, user_profile.cover_image, user_profile.is_verified,
		user_profile.s3_enabled
		FROM restricted_profiles, user_profile WHERE restricted_profiles.restricted_profile=user_profile.profile_seq AND
		restricted_profiles.restricted_by=%s AND restricted_profiles.restriction_type=%s AND 
		restricted_profiles.status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       UPDATE_RESTRICTED_PROFILE_STATUS
	</id>
	<stmt>
        UPDATE restricted_profiles SET unrestricted_on=%s, status=%s WHERE restrict_seq=%s AND restricted_profile=%s AND 
		restricted_by=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_LIKE_NOTIFICATIONS
	</id>
	<stmt>
        SELECT CAST(post_likes.like_seq AS VARCHAR), CAST(post_likes.post_seq AS VARCHAR), 
		CAST(post_likes.liked_by_profile AS VARCHAR), CAST(post_likes.like_time AS VARCHAR),
		user_post_content.media_file, user_post_content.media_type, user_post_content.media_cover,
		user_post_content.s3_enabled
		FROM post_likes, user_posts, user_post_content WHERE post_likes.post_seq=user_posts.post_seq AND
		post_likes.post_seq=user_post_content.post_seq AND post_likes.like_time>=%s AND user_posts.profile_seq=%s
		AND user_posts.status='ACTIVE' AND post_likes.status='ACTIVE' ORDER BY post_likes.like_time DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       CHECK_RESTRICTED_PROFILE_LIST
	</id>
	<stmt>
        SELECT CAST(restrict_seq AS VARCHAR), CAST(restricted_by AS VARCHAR) FROM restricted_profiles 
		WHERE restricted_profile=%s AND status=%s AND restricted_by IN (<PROFILE_LIST>) AND restriction_type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_COMMENT_NOTIFICATIONS
	</id>
	<stmt>
        SELECT CAST(post_comments.comment_seq AS VARCHAR), CAST(post_comments.comment_time AS VARCHAR), 
		CAST(user_posts.post_seq AS VARCHAR),
		user_post_content.media_file, user_post_content.media_type, user_post_content.media_cover,
		CAST(post_comments.comment_profile_seq AS VARCHAR), user_post_content.s3_enabled
		FROM post_comments, user_posts, user_post_content WHERE post_comments.content_seq=user_posts.post_seq AND
		post_comments.content_seq=user_post_content.post_seq AND
		post_comments.content_type=%s AND post_comments.status=%s AND user_posts.profile_seq=%s AND
		post_comments.comment_time>=%s AND post_comments.status='ACTIVE' AND user_posts.status='ACTIVE'
		ORDER BY post_comments.comment_time DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_POST_SUBSCRIBE_NOTIFICATIONS
	</id>
	<stmt>
        SELECT CAST(post_subscription.subscription_seq AS VARCHAR), CAST(post_subscription.subscribed_on AS VARCHAR),
		CAST(user_profile.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.display_name, 
		user_profile.profile_picture,
		user_profile.cover_image, CAST(user_post_content.post_seq AS VARCHAR), 
		user_post_content.media_file, user_post_content.media_type,
		user_post_content.media_cover, user_profile.s3_enabled AS s3_profile, user_post_content.s3_enabled AS s3_post,
		user_profile.is_verified
		FROM post_subscription, user_profile, user_post_content WHERE post_subscription.subscribed_by_seq=user_profile.profile_seq AND
		post_subscription.subscribed_post_seq=user_post_content.post_seq AND post_subscription.subscribed_to_seq=%s AND 
		post_subscription.subscribed_on>=%s AND post_subscription.status=%s ORDER BY post_subscription.subscribed_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_PROFILE_FOLLOW_NOTIFICATIONS
	</id>
	<stmt>
        SELECT CAST(user_profile.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.display_name, 
		user_profile.profile_picture, user_profile.cover_image, CAST(profile_follower.followed_on AS VARCHAR),
		user_profile.s3_enabled, user_profile.is_verified
		FROM profile_follower, user_profile WHERE profile_follower.follower_profile_seq=user_profile.profile_seq
		AND profile_follower.following_profile_seq=%s AND profile_follower.status='ACTIVE' AND 
		profile_follower.followed_on>=%s ORDER BY profile_follower.followed_on DESC		
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
       GET_PROFILE_SUBSCRIBE_NOTIFICATIONS
	</id>
	<stmt>
        SELECT CAST(profile_subscription.subscription_seq AS VARCHAR), user_profile.user_handle, 
		user_profile.display_name,
		user_profile.profile_picture, user_profile.cover_image, CAST(user_profile.profile_seq AS VARCHAR), 
		CAST(profile_charges.paid_on AS VARCHAR),
		user_profile.is_verified, user_profile.s3_enabled
		FROM profile_subscription, profile_charges, user_profile
		WHERE profile_subscription.charge_seq=profile_charges.charge_seq AND 
		profile_subscription.subscribed_by_seq=user_profile.profile_seq AND
		profile_subscription.subscribed_to_seq=%s AND profile_subscription.status=%s AND profile_charges.paid_on>=%s
		ORDER BY profile_charges.paid_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_PAYMENTS_MADE
	</id>
	<stmt>
        SELECT SUM(paid_amount) FROM profile_charges WHERE profile_seq=%s AND paid_on>=%s and paid_on<=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
       GET_PAYMENTS_RECEIVED_FOR_PROFILE
	</id>
	<stmt>
        SELECT SUM(paid_amount) FROM profile_charges WHERE paid_key=%s AND paid_on>=%s and paid_on<=%s AND paid_for='PROFILE' 
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_PAYMENTS_RECEIVED_FOR_POSTS
	</id>
	<stmt>
        SELECT SUM(profile_charges.paid_amount) FROM profile_charges, user_posts 
		WHERE profile_charges.paid_key=user_posts.post_seq AND 
		user_posts.profile_seq=%s AND profile_charges.paid_on>=%s AND profile_charges.paid_on<=%s AND  
		profile_charges.paid_for='POST'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_PAYMENTS_RECEIVED_FOR_REFERRALS
	</id>
	<stmt>
        SELECT SUM(amount_earned) FROM referral_earning WHERE earned_by=%s AND earned_on>=%s and earned_on<=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       GET_ALERT_SETTINGS
	</id>
	<stmt>
        SELECT enable_email_alerts AS email_alert FROM user_profile WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       UPDATE_ALERT_SETTINGS
	</id>
	<stmt>
        UPDATE user_profile SET enable_email_alerts=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       UPDATE_ALERT_STATUS
	</id>
	<stmt>
        UPDATE user_profile_alert SET status=%s WHERE profile_seq=%s AND type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_USER_ALERTS
	</id>
	<stmt>
        SELECT CAST(alert_seq AS VARCHAR), message, can_dismiss, type FROM user_profile_alert WHERE profile_seq=%s AND
		status=%s LIMIT 1 OFFSET 0
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      UPDATE_ALERT_DISPLAY_STATUS
	</id>
	<stmt>
        UPDATE user_profile_alert SET status=%s, is_displayed=%s WHERE alert_seq=%s AND profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_PROFILE_CATEGORIES
	</id>
	<stmt>
        SELECT profile_category.category, config_meta_data.display_value FROM profile_category, config_meta_data 
		WHERE profile_category.category=config_meta_data.config_key AND profile_category.profile_seq=%s AND config_meta_data.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      DELETE_PROFILE_CATEGORIES
	</id>
	<stmt>
        DELETE FROM profile_category WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      INSERT_PROFILE_CATEGORY
	</id>
	<stmt>
        INSERT INTO profile_category (profile_seq, category) VALUES (%s,%s) RETURNING CAST(profile_cat_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_PROFILE_SUBSCRIPTION_LIST
	</id>
	<stmt>
        SELECT CAST(subscribed_to_seq AS VARCHAR), CAST(subscription_seq AS VARCHAR) 
		FROM profile_subscription WHERE subscribed_to_seq IN (<PROFILE_LIST>) AND
		subscribed_by_seq=%s AND status=%s AND from_date<=%s AND to_date>=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      UPDATE_POST_TYPE
	</id>
	<stmt>
        UPDATE user_posts SET post_type=%s, viewer_fee=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      INSERT_VIDEO_DURATION_POST
	</id>
	<stmt>
        UPDATE user_post_content SET video_duration=%s WHERE content_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      INSERT_VIDEO_DURATION_STORY
	</id>
	<stmt>
        UPDATE user_stories SET video_duration=%s WHERE story_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      CHECK_USER_HANDLE
	</id>
	<stmt>
        SELECT CAST(profile_seq AS VARCHAR) FROM user_profile WHERE user_handle=%s AND profile_seq!=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_RESTRICTED_BY
	</id>
	<stmt>
        SELECT CAST(restricted_by AS VARCHAR) FROM restricted_profiles WHERE restricted_profile=%s AND status=%s AND restriction_type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      UPDATE_SUBSCRIBED_USERS
	</id>
	<stmt>
        UPDATE profile_subscription SET status=%s WHERE subscribed_to_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_REFERRAL_CODE
	</id>
	<stmt>
        SELECT referral_code FROM user_profile WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      CHECK_DUPLICATE_CODE
	</id>
	<stmt>
        SELECT CAST(profile_seq AS VARCHAR) FROM user_profile WHERE referral_code=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      UPDATE_REFERAL_CODE
	</id>
	<stmt>
        UPDATE user_profile SET referral_code=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      UPDATE_REFERAL_CODE_USED
	</id>
	<stmt>
        UPDATE user_master SET used_referral_seq=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      INSERT_USER_REFERAL
	</id>
	<stmt>
        INSERT INTO user_referral (user_seq, used_by_seq, used_on, status, referral_pc, referral_duration)
		VALUES (%s,%s,%s,%s,%s,%s) RETURNING CAST (referral_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_USER_FOR_REFERRAL
	</id>
	<stmt>
        SELECT CAST(user_seq AS VARCHAR) FROM user_profile WHERE referral_code=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      UPDATE_REFERRAL_CODE_STATUS
	</id>
	<stmt>
        UPDATE user_referral SET status=%s WHERE referral_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_REFERRAL_DETAILS
	</id>
	<stmt>
       SELECT CAST(user_seq AS VARCHAR), referral_pc, referral_duration, CAST(used_on AS VARCHAR)
	   FROM user_referral WHERE referral_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      INSERT_REFERRAL_EARNED
	</id>
	<stmt>
       INSERT INTO referral_earning (referral_seq, earned_on, amount_earned, earned_by, gst, tds, charges, payable) 
	   VALUES (%s,%s,%s,%s,%s,%s,%s,%s) RETURNING CAST(earning_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_REFERRAL_EARNED_USERS
	</id>
	<stmt>
       SELECT CAST(user_referral.used_on as VARCHAR), user_profile.user_handle, user_profile.display_name,
	   user_profile.cover_image, user_profile.profile_picture, CAST(user_profile.profile_seq AS VARCHAR),
	   user_profile.s3_enabled
	   FROM user_referral, user_profile 
	   WHERE user_referral.used_by_seq=user_profile.user_seq AND user_referral.user_seq=%s AND
	   user_referral.status='ACTIVE' ORDER BY user_referral.used_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_REFERRAL_EARNED_USERS_COUNT
	</id>
	<stmt>
       SELECT COUNT(referral_seq) FROM user_referral WHERE user_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
      GET_LAST_PAYOUT
	</id>
	<stmt>
       SELECT MAX(requested_month) AS last_payout FROM user_payout_log WHERE requested_by=%s AND 
	   status!='CANCELLED'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_POST_EARNINGS
	</id>
	<stmt>
        SELECT CAST(MIN(profile_charges.paid_on) AS VARCHAR) AS first_pay
		FROM profile_charges, user_posts WHERE profile_charges.paid_key=user_posts.post_seq AND 
		profile_charges.paid_for='POST' AND user_posts.profile_seq=%s				
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_PROFILE_EARNINGS
	</id>
	<stmt>
        SELECT CAST(MIN(paid_on) AS VARCHAR) AS first_pay 
		FROM profile_charges WHERE paid_key=%s AND paid_for='PROFILE' 		
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_REFERRAL_EARNINGS
	</id>
	<stmt>
        SELECT CAST(MIN(earned_on) AS VARCHAR) AS first_pay FROM referral_earning WHERE earned_by=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_EARNINGS
	</id>
	<stmt>
        SELECT SUM(profile_charges.paid_amount) AS amount_earned
		FROM profile_charges, user_posts WHERE profile_charges.paid_key=user_posts.post_seq AND 
		profile_charges.paid_for='POST' AND user_posts.profile_seq=%s AND profile_charges.paid_on>=%s 
		AND profile_charges.paid_on<%s				
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_EARNINGS
	</id>
	<stmt>
        SELECT SUM(paid_amount) AS amount_earned FROM profile_charges WHERE paid_key=%s AND paid_for='PROFILE' 
		AND paid_on>=%s AND paid_on<%s				
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_REFERRAL_EARNINGS
	</id>
	<stmt>
        SELECT SUM(amount_earned) AS amount_earned FROM referral_earning WHERE earned_by=%s AND
		earned_on>=%s AND earned_on<%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_PAYOUT_LOG
	</id>
	<stmt>
        INSERT INTO user_payout_log (requested_on, requested_by, requested_month, total_payout,
		referral_payout, subs_payout, status) VALUES (%s, %s, %s, %s, %s, %s, %s) 
		RETURNING CAST(payout_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYOUT_REQUESTS
	</id>
	<stmt>
        SELECT requested_month, total_payout, referral_payout, subs_payout, 
		CAST(requested_on AS VARCHAR) FROM user_payout_log WHERE requested_by=%s 
		AND status=%s ORDER BY requested_month ASC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
        GET_PAYOUT_REQUESTS_HIST
	</id>
	<stmt>
        SELECT requested_month, total_payout, referral_payout, subs_payout, 
		CAST(requested_on AS VARCHAR), CAST(settled_on as VARCHAR) FROM user_payout_log WHERE requested_by=%s 
		AND status=%s ORDER BY requested_month DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYOUT_REQUESTS_HIST_COUNT
	</id>
	<stmt>
        SELECT COUNT(payout_seq) FROM user_payout_log WHERE requested_by=%s 
		AND status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_GSTIN
	</id>
	<stmt>
        SELECT gstin FROM user_master WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_GSTIN_PROFILE
	</id>
	<stmt>
        SELECT user_master.gstin FROM user_profile, user_master WHERE user_profile.user_seq=user_master.user_seq 
		AND user_profile.profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_GSTIN
	</id>
	<stmt>
        UPDATE user_master SET gstin=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_GSTIN_PAN
	</id>
	<stmt>
        UPDATE user_master SET gstin=%s, pan=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_LIKE_USER_DETAILS
	</id>
	<stmt>
        SELECT CAST(user_profile.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.profile_picture,
		user_profile.display_name, user_profile.is_verified, CAST(post_likes.like_time AS VARCHAR),
		user_profile.s3_enabled
		FROM post_likes, user_profile WHERE post_likes.liked_by_profile=user_profile.profile_seq AND 
		post_likes.post_seq=%s AND post_likes.status='ACTIVE' ORDER BY post_likes.like_time DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_LIKE_USER_DETAILS_COUNT
	</id>
	<stmt>
        SELECT COUNT(like_seq) FROM post_likes WHERE post_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_POST_LIKE_USER
	</id>
	<stmt>
        SELECT CAST(user_profile.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.profile_picture,
		user_profile.display_name, user_profile.is_verified, CAST(post_likes.like_time AS VARCHAR),
		user_profile.s3_enabled
		FROM post_likes, user_profile WHERE post_likes.liked_by_profile=user_profile.profile_seq AND 
		post_likes.post_seq=%s AND (user_profile.user_handle ILIKE %s OR user_profile.display_name ILIKE %s)
		AND post_likes.status='ACTIVE' ORDER BY post_likes.like_time DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SEARCH_POST_LIKE_USER_COUNT
	</id>
	<stmt>
        SELECT COUNT(post_likes.like_seq)
		FROM post_likes, user_profile WHERE post_likes.liked_by_profile=user_profile.profile_seq AND 
		post_likes.post_seq=%s AND (user_profile.user_handle ILIKE %s OR user_profile.display_name ILIKE %s)
		AND post_likes.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_FEEDBACK_DETAILS
	</id>
	<stmt>
        SELECT CAST(feedback_seq AS VARCHAR) FROM post_report_feedback WHERE report_seq=%s		
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_FEEDBACK_DETAILS
	</id>
	<stmt>
        INSERT INTO post_report_feedback (report_seq, feedback_by, feedback_on, feedback_rating, 
		feedback_comment, status) VALUES (%s, %s, %s, %s, %s, %s) RETURNING CAST(feedback_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_POST_COMMENT_STATUS
	</id>
	<stmt>
        UPDATE post_comments SET status=%s WHERE comment_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_POST_COMMENTS
	</id>
	<stmt>
        UPDATE user_posts SET post_comments=%s WHERE post_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_FCM_KEY
	</id>
	<stmt>
        UPDATE user_master SET fcm_key=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_FCM_KEY
	</id>
	<stmt>
        SELECT fcm_key FROM user_master WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_PROFILE_SEQ
	</id>
	<stmt>
        SELECT CAST(profile_seq AS VARCHAR) FROM user_profile WHERE user_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_USER_SEQ
	</id>
	<stmt>
        SELECT CAST(user_seq AS VARCHAR) FROM user_profile WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_COMISSION
	</id>
	<stmt>
        SELECT percentage FROM user_comission WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_RAZORPAY_CONTACT
	</id>
	<stmt>
        UPDATE user_profile SET razorpay_profile=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_RAZORPAY_ACCOUNT
	</id>
	<stmt>
        UPDATE user_profile SET razorpay_acc=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_ACCOUNT_DETAILS
	</id>
	<stmt>
        SELECT account_holder, account_type, bank_account, ifsc_code, profile_seq FROM user_profile WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_SEQ
	</id>
	<stmt>
        SELECT CAST(user_profile.user_seq AS VARCHAR), CAST(user_profile.profile_seq AS VARCHAR), user_master.fcm_key 
		FROM user_profile, user_master WHERE user_profile.user_seq=user_master.user_seq AND user_profile.user_handle=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_POST_TAG
	</id>
	<stmt>
        INSERT INTO post_tags (tagged_by, tagged_to, tagged_on, post_seq, type) VALUES (%s, %s, %s, %s, %s) 
		RETURNING CAST(tag_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        REMOVE_POST_TAG
	</id>
	<stmt>
        DELETE FROM post_tags WHERE tagged_by=%s AND type=%s AND post_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_TAGGED_USERS
	</id>
	<stmt>
        SELECT CAST(post_tags.tag_seq AS VARCHAR), CAST(user_profile.profile_seq AS VARCHAR),
		user_profile.user_handle, user_profile.cover_image, user_profile.display_name, user_profile.s3_enabled,
		user_profile.profile_picture FROM post_tags, user_profile 
		WHERE post_tags.tagged_to=user_profile.profile_seq AND post_tags.post_seq=%s AND 
		user_profile.status='ACTIVE' AND post_tags.type='POST' AND post_tags.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POSTS_DETAILS_TAGGED
	</id>
	<stmt>
		SELECT CAST(user_posts.post_seq AS VARCHAR), user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee, 
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, user_post_content.media_file, 
		user_post_content.media_type, user_post_content.media_cover,
		user_posts.block_reason, user_post_content.video_duration, 
		CAST(user_post_content.content_seq AS VARCHAR), user_post_content.fuzzy_image,
		user_post_content.s3_enabled, CAST(user_profile.profile_seq AS VARCHAR),
		user_profile.user_handle, user_profile.profile_picture, user_profile.display_name
        FROM user_posts, user_post_content, user_profile WHERE 		
		user_posts.post_seq=user_post_content.post_seq AND 
		user_posts.profile_seq=user_profile.profile_seq AND user_profile.status='ACTIVE' AND
		user_posts.post_seq IN (<POST_LIST>) AND user_post_content.status='ACTIVE' AND user_posts.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POSTS_TAGGED
	</id>
	<stmt>
        SELECT MAX(post_tags.tagged_on) AS tagged_on, CAST(post_tags.post_seq AS VARCHAR) FROM post_tags 
		WHERE post_tags.tagged_to=%s AND post_tags.type='POST' AND post_tags.status='ACTIVE'
		GROUP BY post_tags.post_seq		
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POSTS_TAGGED_COUNT
	</id>
	<stmt>
        SELECT COUNT(DISTINCT(post_tags.post_seq)) FROM post_tags 
		WHERE post_tags.tagged_to=%s AND post_tags.type='POST' AND post_tags.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_TAG_LIST
	</id>
	<stmt>
        SELECT DISTINCT(CAST(post_seq AS VARCHAR)) FROM post_tags 
		WHERE post_seq IN (<POST_LIST>) AND type='POST' AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_TAG_COUNT
	</id>
	<stmt>
        SELECT COUNT(tag_seq) FROM post_tags WHERE post_seq=%s AND type='POST' AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_FOR_HANDLE
	</id>
	<stmt>
        SELECT profile_seq, user_handle FROM user_profile WHERE user_handle IN (<TAG_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_FOLLOWING_PROFILES
	</id>
	<stmt>
        SELECT CAST(profile_follower.follower_profile_seq AS VARCHAR) FROM profile_follower, user_profile WHERE 
		profile_follower.follower_profile_seq=user_profile.profile_seq AND 
		profile_follower.following_profile_seq=%s AND profile_follower.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SUBSCRIBING_PROFILES
	</id>
	<stmt>
        SELECT CAST(subscribed_by_seq AS VARCHAR) FROM profile_subscription WHERE subscribed_to_seq=%s
		AND status='ACTIVE' AND from_date<=%s AND to_date>=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
        GET_FOLLOWED_BY_PROFILES
	</id>
	<stmt>
        SELECT CAST(profile_follower.following_profile_seq AS VARCHAR) FROM profile_follower, user_profile
		WHERE profile_follower.follower_profile_seq=user_profile.profile_seq AND 
		profile_follower.follower_profile_seq=%s 
		AND profile_follower.status='ACTIVE' AND profile_follower.following_profile_seq IN (<PROFILES_LIST>)		
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
        GET_SUBSCRIBED_BY_PROFILES
	</id>
	<stmt>
        SELECT CAST(subscribed_to_seq AS VARCHAR) FROM profile_subscription WHERE subscribed_by_seq=%s
		AND status='ACTIVE' AND from_date<=%s AND to_date>=%s AND subscribed_to_seq IN (<PROFILES_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_DETAILS_LIST
	</id>
	<stmt>
        SELECT user_profile.user_handle, user_profile.display_name, CAST(user_master.user_seq AS VARCHAR),
		CAST(user_profile.profile_seq AS VARCHAR), user_profile.profile_picture, user_profile.s3_enabled
        FROM user_profile, user_master WHERE user_profile.user_seq=user_master.user_seq AND 
		user_profile.profile_seq IN (<PROFILES_LIST>) AND user_profile.status='ACTIVE' AND user_master.status='ACTIVE'
		AND (user_profile.user_handle LIKE %s or user_profile.display_name LIKE %s) ORDER BY user_profile.user_handle
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_DETAILS_LIST_COUNT
	</id>
	<stmt>
        SELECT COUNT(user_profile.user_handle)
        FROM user_profile, user_master WHERE user_profile.user_seq=user_master.user_seq AND 
		user_profile.profile_seq IN (<PROFILES_LIST>) AND user_profile.status='ACTIVE' AND user_master.status='ACTIVE'
		AND (user_profile.user_handle LIKE %s or user_profile.display_name LIKE %s)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_FOLLOWING
	</id>
	<stmt>
        SELECT CAST(follower_seq AS VARCHAR) FROM profile_follower WHERE follower_profile_seq=%s AND
		following_profile_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_SUBSCRIBING
	</id>
	<stmt>
        SELECT CAST(subscription_seq AS VARCHAR) FROM profile_subscription WHERE subscribed_by_seq=%s AND
		subscribed_to_seq=%s AND status='ACTIVE' AND from_date<=%s AND to_date>=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_TAG_NOTIFICATIONS
	</id>
	<stmt>
        SELECT CAST(post_tags.tag_seq AS VARCHAR), CAST(post_tags.post_seq AS VARCHAR) AS content_seq, 
		CAST(post_tags.tagged_on AS VARCHAR), post_tags.type,
		CAST(user_profile.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.profile_picture,
		user_profile.display_name, user_profile.is_verified, user_profile.s3_enabled 
		FROM post_tags, user_profile
		WHERE post_tags.tagged_by=user_profile.profile_seq AND post_tags.tagged_to=%s AND
		user_profile.status='ACTIVE' AND post_tags.status='ACTIVE'
		ORDER BY post_tags.tagged_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_TAG_NOTIFICATIONS_COUNT
	</id>
	<stmt>
        SELECT COUNT(post_tags.tag_seq) FROM post_tags, user_profile
		WHERE post_tags.tagged_by=user_profile.profile_seq AND post_tags.tagged_to=%s AND
		user_profile.status='ACTIVE' AND post_tags.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_TAG_NOTIFICATIONS_COUNT_DATE
	</id>
	<stmt>
        SELECT COUNT(post_tags.tag_seq) FROM post_tags, user_profile
		WHERE post_tags.tagged_by=user_profile.profile_seq AND post_tags.tagged_to=%s AND
		user_profile.status='ACTIVE' AND post_tags.tagged_on>=%s AND post_tags.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_STORY_DETAILS
	</id>
	<stmt>
        SELECT CAST(user_stories.profile_seq AS VARCHAR), user_profile.user_handle, user_profile.display_name, 
		user_profile.profile_picture,
        user_stories.media_file, user_stories.media_type, user_stories.media_format, 
		user_stories.story_comments, CAST(user_stories.story_seq AS VARCHAR), 
        user_stories.media_cover, user_profile.is_verified, user_stories.video_duration, user_stories.status,
		user_stories.block_reason, user_profile.s3_enabled AS s3_profile, user_stories.s3_enabled AS s3_story
        FROM user_stories, user_profile WHERE user_stories.profile_seq=user_profile.profile_seq AND
        user_stories.story_seq=%s AND user_stories.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_HANDLE_HISTORY
	</id>
	<stmt>
        INSERT INTO user_handles (profile_seq, new_handle, old_handle) VALUES (%s,%s,%s) 
		RETURNING CAST(handle_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_HANDLE_HISTORY
	</id>
	<stmt>
        SELECT new_handle, profile_seq, old_handle FROM user_handles WHERE old_handle IN (<TAG_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_GSTIN_LIST
	</id>
	<stmt>
        SELECT user_master.gstin, user_profile.profile_seq FROM user_profile, user_master WHERE user_profile.user_seq=user_master.user_seq 
		AND user_profile.profile_seq IN (<PROFILE_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_NOTIFICATION_VIEW
	</id>
	<stmt>
        UPDATE notification_view SET last_viewed=%s WHERE notification_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_NOTIFICATION_VIEW
	</id>
	<stmt>
        SELECT last_viewed, CAST(notification_seq AS VARCHAR) FROM notification_view 
		WHERE profile_seq=%s AND view_type=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_NOTIFICATION_VIEW_ALL
	</id>
	<stmt>
        SELECT last_viewed, CAST(notification_seq AS VARCHAR), view_type FROM notification_view 
		WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_NOTIFICATION_VIEW
	</id>
	<stmt>
        INSERT INTO notification_view (last_viewed, profile_seq, view_type) VALUES (%s,%s,%s)
		RETURNING CAST(notification_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENTS_POST
	</id>
	<stmt>
        SELECT CAST(profile_charges.charge_seq AS VARCHAR), 
		profile_charges.paid_amount, CAST(profile_charges.paid_on as VARCHAR),
		user_master.full_name, user_master.email_id, user_master.mobile_number, user_profile.user_handle, 
		user_profile.display_name, CAST(user_master.user_seq AS VARCHAR), profile_charges.gst, profile_charges.inward_charges,
		profile_charges.outward_charges, profile_charges.comission, profile_charges.gst_comission, profile_charges.tcs,
		profile_charges.tds, profile_charges.payable
		FROM profile_charges, user_posts, user_profile, user_master WHERE profile_charges.paid_key=user_posts.post_seq AND
		user_posts.profile_seq=user_profile.profile_seq AND user_profile.user_seq=user_master.user_seq AND 
		profile_charges.paid_for='POST' AND profile_charges.paid_on>=%s AND profile_charges.paid_on<=%s
		AND user_profile.profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENTS_PROFILE
	</id>
	<stmt>
        SELECT CAST(profile_charges.charge_seq AS VARCHAR), 
		profile_charges.paid_amount, CAST(profile_charges.paid_on as VARCHAR),
		user_master.full_name, user_master.email_id, user_master.mobile_number, user_profile.user_handle, 
		user_profile.display_name, CAST(user_master.user_seq AS VARCHAR), profile_charges.gst, profile_charges.inward_charges,
		profile_charges.outward_charges, profile_charges.comission, profile_charges.gst_comission, profile_charges.tcs,
		profile_charges.tds, profile_charges.payable
		FROM profile_charges, user_profile, user_master WHERE profile_charges.paid_key=user_profile.profile_seq AND 
		user_profile.user_seq=user_master.user_seq AND 
		profile_charges.paid_for='PROFILE' AND profile_charges.paid_on>=%s AND profile_charges.paid_on<=%s
		AND user_profile.profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PAYMENTS_REFERRAL
	</id>
	<stmt>
        SELECT CAST(referral_earning.earning_seq AS VARCHAR), 
		referral_earning.amount_earned AS paid_amount, CAST(referral_earning.earned_on as VARCHAR) AS paid_on,
		user_master.full_name, user_master.email_id, user_master.mobile_number, user_profile.user_handle, 
		user_profile.display_name, CAST(user_master.user_seq AS VARCHAR), referral_earning.gst, referral_earning.tds,
		referral_earning.charges AS outward_charges, referral_earning.payable
		FROM referral_earning, user_referral, user_profile, user_master WHERE 
		referral_earning.referral_seq=user_referral.referral_seq AND 
		user_referral.used_by_seq=user_master.user_seq AND user_referral.used_by_seq=user_profile.user_seq
		AND referral_earning.earned_on>=%s AND referral_earning.earned_on<=%s
		AND user_profile.profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_USER
	</id>
	<stmt>
        SELECT user_master.email_id FROM user_posts, user_profile, user_master 
		WHERE user_posts.profile_seq=user_profile.profile_seq AND
		user_profile.user_seq=user_master.user_seq AND user_posts.post_seq=%s AND user_posts.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_USER
	</id>
	<stmt>
        SELECT user_master.email_id FROM user_profile, user_master 
		WHERE user_profile.user_seq=user_master.user_seq AND user_profile.profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_FOR_STORY
	</id>
	<stmt>
        SELECT user_master.email_id FROM user_stories, user_profile, user_master 
		WHERE user_stories.profile_seq=user_profile.profile_seq AND
		user_profile.user_seq=user_master.user_seq AND user_stories.story_seq=%s AND user_stories.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_EMAIL_FOR_USER
	</id>
	<stmt>
        SELECT email_id FROM user_master WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_USER_COMMENT
	</id>
	<stmt>
        SELECT user_master.email_id FROM post_comments, user_posts, user_profile, user_master
		WHERE post_comments.content_seq=user_posts.post_seq AND user_posts.profile_seq=user_profile.profile_seq
		AND user_profile.user_seq=user_master.user_seq AND post_comments.comment_seq=%s 
		AND post_comments.content_type='POST' AND user_posts.status='ACTIVE' AND post_comments.status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POPULAR_CATEGORIES
	</id>
	<stmt>
        SELECT COUNT(profile_seq) AS profile_count, category FROM profile_category GROUP BY category 
		ORDER BY  profile_count DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_VALUES_FOR_CODES
	</id>
	<stmt>
        SELECT config_key, display_value FROM config_meta_data WHERE config_key IN (<CONFIG_KEYS>) AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_POST_LIKE_STATUS
	</id>
	<stmt>
        UPDATE post_likes SET status=%s WHERE liked_by_profile=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_POST_COMMENTS_STATUS
	</id>
	<stmt>
        UPDATE post_comments SET status=%s WHERE comment_profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_POST_COMMENT_LIKES_STATUS
	</id>
	<stmt>
        UPDATE post_comment_likes SET status=%s WHERE liked_by_profile=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_POST_TAGS_STATUS
	</id>
	<stmt>
        UPDATE post_tags SET status=%s WHERE (tagged_by=%s OR tagged_to=%s)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_PROFILE_FOLLOWER_STATUS
	</id>
	<stmt>
        UPDATE profile_follower SET status=%s WHERE (follower_profile_seq=%s OR following_profile_seq=%s)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_PROFILE_SUBSCRIPTION_STATUS
	</id>
	<stmt>
        UPDATE profile_subscription SET status=%s WHERE (subscribed_by_seq=%s OR subscribed_to_seq=%s)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_USER_POST_STATUS
	</id>
	<stmt>
        UPDATE user_posts SET status=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_USER_STORIES_STATUS
	</id>
	<stmt>
        UPDATE user_stories SET status=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
       SET_POST_SUBSCRIPTION_STATUS
	</id>
	<stmt>
        UPDATE post_subscription SET status=%s WHERE (subscribed_by_seq=%s OR subscribed_to_seq=%s)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_VIEW_PROFILE
	</id>
	<stmt>
        SELECT CAST(view_seq AS VARCHAR) FROM post_views WHERE post_seq=%s AND liked_by_profile=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        SAVE_POST_VIEW
	</id>
	<stmt>
        INSERT INTO post_views (post_seq, liked_by_profile, like_time) VALUES (%s,%s,%s) RETURNING 
		CAST(view_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_COUNT_POST_VIEW
	</id>
	<stmt>
        SELECT COUNT(view_seq) FROM post_views WHERE post_seq=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
        CHECK_POST_VIEW
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR) FROM post_views WHERE liked_by_profile=%s AND post_seq IN (<POST_SEQ_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_SHARES
	</id>
	<stmt>
        SELECT count FROM post_shares WHERE post_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_POST_SHARES
	</id>
	<stmt>
        UPDATE post_shares SET count = %s WHERE post_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        ADD_POST_SHARES
	</id>
	<stmt>
        INSERT INTO post_shares (count, post_seq) VALUES (%s,%s) RETURNING CAST(share_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_VERIFY_NOTIFIED
	</id>
	<stmt>
        UPDATE user_profile SET is_verify_notified=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_DETAILS_MOBILE
	</id>
	<stmt>
        SELECT user_seq, email_id, status FROM user_master WHERE mobile_number=%s AND (status=%s OR status=%s)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_DETAILS_MOBILE_ACTIVE
	</id>
	<stmt>
        SELECT user_seq, email_id, status, last_profile, password, is_personalized
		FROM user_master WHERE mobile_number=%s AND status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>


<query>
	<id>
        DELETE_PENDING_ACC
	</id>
	<stmt>
        UPDATE user_master SET status=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_MOBILE_OTP
	</id>
	<stmt>
        UPDATE user_master SET mobile_otp=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_MOBILE_OTP
	</id>
	<stmt>
        SELECT mobile_otp FROM user_master WHERE mobile_number=%s AND status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_MOBILE_OTP_EMAIL
	</id>
	<stmt>
        SELECT mobile_otp FROM user_master WHERE mobile_number=%s AND email_id=%s AND status='ACTIVE'
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        CHECK_DUP_EMAIL
	</id>
	<stmt>
        SELECT CAST(user_seq AS VARCHAR) FROM user_master WHERE email_id ILIKE %s AND status=%s AND user_seq!=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_EMAIL_OTP
	</id>
	<stmt>
        SELECT email_otp, user_seq FROM user_master WHERE mobile_number=%s AND status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_MOBILE
	</id>
	<stmt>
        UPDATE user_master SET mobile_number=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        DEACTIVATE_USER
	</id>
	<stmt>
        UPDATE user_master SET status=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        DEACTIVATE_PROFILE
	</id>
	<stmt>
        UPDATE user_profile SET status=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_PROFILE
	</id>
	<stmt>
        SELECT CAST(profile_seq AS VARCHAR) FROM user_profile WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_EMAIL
	</id>
	<stmt>
        UPDATE user_master SET email_id=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_EMAIL_OTP
	</id>
	<stmt>
        UPDATE user_master SET email_otp=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PROFILE_FOR_USER
	</id>
	<stmt>
        SELECT profile_seq FROM user_profile WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_HANDLE
	</id>
	<stmt>
        UPDATE user_profile SET user_handle=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_NAME
	</id>
	<stmt>
        UPDATE user_master SET full_name=%s, email_id=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_PROFILE_DISPLAY_NAME
	</id>
	<stmt>
        UPDATE user_profile SET display_name=%s, user_handle=%s WHERE profile_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_ALL_INTERESTS
	</id>
	<stmt>
        SELECT interest_name FROM interest_master WHERE status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_ALL_INTEREST_CATEGORIES
	</id>
	<stmt>
        SELECT DISTINCT(category) AS category FROM interest_master WHERE status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_INTERESTS
	</id>
	<stmt>
        SELECT interest_name FROM user_interests WHERE user_seq=%s AND status=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        DELETE_USER_INTERESTS
	</id>
	<stmt>
        DELETE FROM user_interests WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_USER_INTEREST
	</id>
	<stmt>
        INSERT INTO user_interests (user_seq, interest_name, status) VALUES(%s,%s,%s) RETURNING CAST(interest_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_UI_COLOUR
	</id>
	<stmt>
        UPDATE user_master SET ui_colour=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        UPDATE_USER_PERSONALIZED
	</id>
	<stmt>
        UPDATE user_master SET is_personalized=%s WHERE user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_POST_TOPIC
	</id>
	<stmt>
        INSERT INTO post_topics (post_seq, topic) VALUES (%s,%s) RETURNING CAST(post_topic_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        INSERT_POST_REACTION
	</id>
	<stmt>
        INSERT INTO post_reactions (post_seq, reaction) VALUES (%s,%s) RETURNING CAST(post_reaction_seq AS VARCHAR)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_REACTIONS
	</id>
	<stmt>
        SELECT reaction FROM post_reactions WHERE post_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_POST_REACTIONS_LIST
	</id>
	<stmt>
        SELECT CAST(post_seq AS VARCHAR), reaction FROM post_reactions WHERE post_seq IN (<POST_LIST>)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_SHOWS_PROFILE
	</id>
	<stmt>
        SELECT COUNT(show_master.show_seq) AS show_count FROM
		show_master, user_profile WHERE show_master.user_seq=user_profile.user_seq AND user_profile.profile_seq=%s AND
		show_master.approval_status='APPROVED' AND show_master.status='ACTIVE' 
		AND (show_master.expiry_date IS NULL OR show_master.expiry_date>%s) AND 
		(show_master.scheduled_on IS NULL OR show_master.scheduled_on<%s)
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_SHOWS
	</id>
	<stmt>
        SELECT show_seq, description, is_paid, price, logo_file, thumb_file, preview_file, s3_enabled, 
		grid_title AS title
		FROM show_master WHERE approval_status='APPROVED' AND status='ACTIVE' AND 
		(expiry_date IS NULL OR expiry_date>%s) AND (scheduled_on IS NULL OR scheduled_on<%s) AND
		user_seq=%s ORDER BY scheduled_on DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PLAYLIST_MASTER
	</id>
	<stmt>
        SELECT user_master.playlist_master_image, user_profile.user_handle, user_profile.display_name
		FROM user_master, user_profile 
		WHERE user_master.user_seq=user_profile.user_seq AND user_master.user_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_EPISODES_SEASON
	</id>
	<stmt>
        SELECT episode_seq, title, description, thumb_file, is_paid, price, preview_file, s3_enabled,
		release_year, banner_file, grid_title
		FROM episode_master WHERE status='ACTIVE' AND approval_status='APPROVED' AND 
		(expiry_date IS NULL OR expiry_date>%s) AND (schedule IS NULL OR schedule<%s) AND
		season_seq=%s ORDER BY schedule DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SEASONS
	</id>
	<stmt>
        SELECT season_seq, title, description, cover_image, release_year, banner_file, grid_title
		FROM season_master WHERE show_seq=%s AND status='ACTIVE' 
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SEASON_DETAILS
	</id>
	<stmt>
        SELECT season_seq, title, description, cover_image, release_year, grid_title
		FROM season_master WHERE season_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SHOW_DETAILS
	</id>
	<stmt>
        SELECT title, description, thumb_file, banner_file, release_year, grid_title FROM show_master WHERE show_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_EPISODES
	</id>
	<stmt>
        SELECT episode_seq, title, description, thumb_file, is_paid, price, preview_file, s3_enabled,
		release_year, banner_file, grid_title
		FROM episode_master WHERE status='ACTIVE' AND approval_status='APPROVED' AND 
		(expiry_date IS NULL OR expiry_date>%s) AND (schedule IS NULL OR schedule<%s) AND
		show_seq=%s ORDER BY schedule DESC
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_EPISODE_CLIPS
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), user_posts.post_comments, 
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee, 
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, user_post_content.media_file, 
		user_post_content.media_type, CAST(user_posts.profile_seq AS VARCHAR), 
        user_post_content.media_cover, user_post_content.fuzzy_image,  user_post_content.s3_enabled,
		user_post_content.video_duration
		FROM user_posts, user_post_content 
		WHERE user_posts.post_seq=user_post_content.post_seq AND user_posts.status='ACTIVE'	AND
		user_posts.is_playlist='YES' AND user_posts.episode_seq=%s
		ORDER BY user_posts.post_seq
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_USER_FOR_EPISODE
	</id>
	<stmt>
        SELECT user_profile.user_handle, user_profile.display_name, user_profile.is_verified,
		user_profile.profile_seq,user_profile.profile_picture, user_profile.s3_enabled
		FROM episode_master, show_master, user_master, user_profile WHERE
		episode_master.show_seq=show_master.show_seq AND show_master.user_seq=user_master.user_seq AND 
		user_master.user_seq = user_profile.user_seq AND episode_master.episode_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_EPISODE_REACTIONS
	</id>
	<stmt>
        SELECT reaction FROM episode_reactions WHERE episode_seq=%s
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_SEASON_CLIPS
	</id>
	<stmt>
        SELECT CAST(user_posts.post_seq AS VARCHAR), user_posts.post_comments,
		CAST(user_posts.posted_on AS VARCHAR), user_posts.post_type, user_posts.viewer_fee,
        CAST(user_posts.expire_on AS VARCHAR), user_posts.status, user_post_content.media_file,
		user_post_content.media_type, CAST(user_posts.profile_seq AS VARCHAR),
        user_post_content.media_cover, user_post_content.fuzzy_image, user_post_content.s3_enabled,
		user_post_content.video_duration, episode_master.title AS episode_title,
		episode_master.description AS episode_description, CAST(episode_master.episode_seq AS VARCHAR)
		FROM user_posts, user_post_content, episode_master
		WHERE user_posts.post_seq=user_post_content.post_seq AND user_posts.episode_seq=episode_master.episode_seq
		AND user_posts.status='ACTIVE' AND user_posts.is_playlist='YES'
		AND episode_master.season_seq=%s AND episode_master.status='ACTIVE'
		ORDER BY episode_master.episode_seq, user_posts.post_seq
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_PLAYLIST
	</id>
	<stmt>
        SELECT show_master.title, show_master.thumb_file, user_profile.profile_seq, show_master.show_seq,
		show_master.s3_enabled, show_master.user_seq, show_master.home_file, show_master.grid_title
		FROM show_master, user_profile 
		WHERE show_master.user_seq=user_profile.user_seq AND user_profile.profile_seq!=%s
		<EXCLUDING> AND show_master.status=%s LIMIT 55 OFFSET 0
	</stmt>
	<dbkey>
            sotruedb
	</dbkey>
	<dbschema>
            sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_GENRE_MASTERS
	</id>
	<stmt>
        SELECT CAST(seq AS VARCHAR), label, value, status FROM genre_master WHERE status='ACTIVE' ORDER BY label
	</stmt>
	<dbkey>
        sotruedb
	</dbkey>
	<dbschema>
        sotrueschema
	</dbschema>
</query>

<query>
	<id>
        GET_TOPIC_MASTERS
	</id>
	<stmt>
        SELECT CAST(seq AS VARCHAR), label, value, status FROM topic_master WHERE status='ACTIVE' ORDER BY label
	</stmt>
	<dbkey>
        sotruedb
	</dbkey>
	<dbschema>
        sotrueschema
	</dbschema>
</query>