"""
Optimized GET_POSTS method for SoTrue Backend
Addresses major performance bottlenecks identified in the original implementation
"""

import json
from typing import Dict, List, Optional, Tuple
import time

class OptimizedSotrueAppUserHandler:
    
    def __init__(self, connDB, session, utils, logger, cache_manager=None):
        self.connDB = connDB
        self.session = session
        self.utils = utils
        self.logger = logger
        self.cache_manager = cache_manager  # Redis/Memcached instance
        self.reactions = ["MEH","LIT","ANGER","AUD","BOO","BLADY","BMAN","CELEB","CLAP","DISG","FEAR","SURP","TEASE","TPASS","WEVER","YAY","YUM"]
    
    def get_posts_optimized(self):
        """
        Optimized version of get_posts with significant performance improvements:
        1. Reduced database queries from 50+ to ~8
        2. Implemented caching for frequently accessed data
        3. Optimized SQL queries with proper JOINs
        4. Eliminated N+1 query problems
        5. Improved browsing history mechanism
        """
        start_time = time.time()
        profile_seq = self.session.get_session_value("_profile_seq")
        
        try:
            # Step 1: Get cached user restrictions and preferences (5-minute cache)
            user_restrictions = self._get_cached_user_restrictions(profile_seq)
            
            # Step 2: Get main posts with single optimized query
            posts_data = self._get_posts_with_metadata(profile_seq, user_restrictions)
            
            if not posts_data:
                return self.create_error("UE012")
            
            # Step 3: Get all related data in batch queries
            post_ids = [post['post_seq'] for post in posts_data]
            profile_ids = list(set([post['profile_seq'] for post in posts_data]))
            
            # Batch fetch all related data
            related_data = self._get_posts_related_data_batch(profile_seq, post_ids, profile_ids)
            
            # Step 4: Enrich posts with related data
            enriched_posts = self._enrich_posts_data(posts_data, related_data)
            
            # Step 5: Update browsing history efficiently
            self._update_browsing_history_optimized(profile_seq, post_ids)
            
            # Step 6: Get playlist data (cached for 10 minutes)
            show_list = self._get_cached_playlist_data(profile_seq)
            
            self.logger.log_info(f"get_posts_optimized completed in {time.time() - start_time:.2f}s")
            
            return self.create_response(
                enriched_posts, 
                "UE011", 
                addnl={
                    "_total_rows": len(enriched_posts),
                    "show_list": show_list,
                    "_performance_time": f"{time.time() - start_time:.2f}s"
                }
            )
            
        except Exception as e:
            self.logger.log_exception(e, "GET_POSTS_OPTIMIZED_ERROR")
            return self.create_error("UE012")
    
    def _get_cached_user_restrictions(self, profile_seq: int) -> Dict:
        """Cache user restrictions for 5 minutes to avoid repeated queries"""
        cache_key = f"user_restrictions:{profile_seq}"
        
        if self.cache_manager:
            cached_data = self.cache_manager.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        
        # Get restrictions in single query
        qparams = [profile_seq, 'ACTIVE', 'BLOCKED']
        restrictions = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_USER_RESTRICTIONS_BATCH", qparams)
        
        restriction_data = {
            'restricted_by': [r.restricted_by for r in restrictions if r.restricted_by],
            'blocked_posts': [r.post_seq for r in restrictions if r.post_seq],
            'blocked_profiles': [r.profile_seq for r in restrictions if r.profile_seq]
        }
        
        if self.cache_manager:
            self.cache_manager.setex(cache_key, 300, json.dumps(restriction_data))  # 5 min cache
        
        return restriction_data
    
    def _get_posts_with_metadata(self, profile_seq: int, restrictions: Dict) -> List[Dict]:
        """Single optimized query to get posts with basic metadata"""
        
        # Build restriction clauses
        restricted_profiles = restrictions.get('restricted_by', [])
        blocked_posts = restrictions.get('blocked_posts', [])
        
        restriction_clause = ""
        if restricted_profiles:
            restriction_clause += f" AND user_posts.profile_seq NOT IN ({','.join(map(str, restricted_profiles))})"
        if blocked_posts:
            restriction_clause += f" AND user_posts.post_seq NOT IN ({','.join(map(str, blocked_posts))})"
        
        # Get browsing history efficiently
        browsing_exclusions = self._get_browsing_exclusions(profile_seq)
        exclusion_clause = ""
        if browsing_exclusions:
            exclusion_clause = f" AND user_posts.post_seq NOT IN ({','.join(map(str, browsing_exclusions))})"
        
        # Media type filter
        media_type = self.params.get("media_type", "ALL")
        media_filter = "%" if media_type == "ALL" else media_type
        
        # Verified filter
        verified_filter = "%" if self.params.get("_start_row", 0) > 100 else "YES"
        
        # Single optimized query with all necessary JOINs
        query_params = [
            profile_seq,  # exclude own posts
            self.utils.get_cur_timestamp(),  # expire_on filter
            media_filter,
            verified_filter
        ]
        
        subs = {
            "<RESTRICTION_CLAUSE>": restriction_clause,
            "<EXCLUSION_CLAUSE>": exclusion_clause
        }
        
        # Use optimized query that gets posts with basic profile info in one go
        posts = self.connDB.execute_prepared_stmt(
            "sotrueappuser", 
            "GET_POSTS_OPTIMIZED", 
            query_params, 
            subs=subs,
            limit=(self.params.get("_start_row", 0), 50)
        )
        
        # Convert to dictionaries and apply random selection
        posts_dict = self.utils.convert_tuples_to_dicts(posts)
        selected_posts, _ = self.select_random(posts_dict)
        
        return selected_posts
    
    def _get_browsing_exclusions(self, profile_seq: int) -> List[int]:
        """Optimized browsing history retrieval"""
        cache_key = f"browsing_history:{profile_seq}"
        
        if self.cache_manager:
            cached_exclusions = self.cache_manager.get(cache_key)
            if cached_exclusions:
                return json.loads(cached_exclusions)
        
        # Get browsing history
        qparams = [profile_seq]
        browsing_data = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_BROWSING_HISTORY", qparams)
        
        exclusions = []
        if browsing_data and browsing_data[0].content_seqs:
            try:
                exclusions = [int(x) for x in browsing_data[0].content_seqs.split(',') if x.strip()]
            except ValueError:
                self.logger.log_error(f"Invalid browsing history data for profile {profile_seq}")
        
        if self.cache_manager:
            self.cache_manager.setex(cache_key, 60, json.dumps(exclusions))  # 1 min cache
        
        return exclusions
    
    def _get_posts_related_data_batch(self, profile_seq: int, post_ids: List[int], profile_ids: List[int]) -> Dict:
        """Fetch all related data in optimized batch queries"""
        
        post_list_str = ','.join(map(str, post_ids))
        profile_list_str = ','.join(map(str, profile_ids))
        
        subs_posts = {"<POST_LIST>": post_list_str}
        subs_profiles = {"<PROFILE_LIST>": profile_list_str}
        
        # Single query to get all user interactions for these posts
        user_interactions = self.connDB.execute_prepared_stmt(
            "sotrueappuser", 
            "GET_USER_POST_INTERACTIONS_BATCH", 
            [profile_seq], 
            subs=subs_posts
        )
        
        # Single query to get all reaction counts for these posts
        reaction_counts = self.connDB.execute_prepared_stmt(
            "sotrueappuser", 
            "GET_POST_REACTION_COUNTS_BATCH", 
            None, 
            subs=subs_posts
        )
        
        # Single query to get all engagement counts (likes, views, shares, comments)
        engagement_counts = self.connDB.execute_prepared_stmt(
            "sotrueappuser", 
            "GET_POST_ENGAGEMENT_COUNTS_BATCH", 
            None, 
            subs=subs_posts
        )
        
        # Get profile data (cached for 30 minutes)
        profile_data = self._get_cached_profile_data(profile_ids)
        
        # Get subscription data
        subscription_data = self.connDB.execute_prepared_stmt(
            "sotrueappuser", 
            "GET_SUBSCRIPTION_DATA_BATCH", 
            [profile_seq, self.utils.get_today_date_str()], 
            subs=subs_profiles
        )
        
        return {
            'user_interactions': self._index_by_post_id(user_interactions),
            'reaction_counts': self._index_reaction_counts(reaction_counts),
            'engagement_counts': self._index_by_post_id(engagement_counts),
            'profile_data': {p.profile_seq: p for p in profile_data},
            'subscription_data': {s.subscribed_to_seq: s for s in subscription_data}
        }
    
    def _get_cached_profile_data(self, profile_ids: List[int]) -> List:
        """Cache profile data for 30 minutes"""
        cache_key = f"profiles:batch:{hash(tuple(sorted(profile_ids)))}"
        
        if self.cache_manager:
            cached_data = self.cache_manager.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        
        profile_list_str = ','.join(map(str, profile_ids))
        subs = {"<PROFILE_LIST>": profile_list_str}
        
        profile_data = self.connDB.execute_prepared_stmt(
            "sotrueappuser", 
            "GET_PROFILE_FOR_USERS", 
            None, 
            subs=subs
        )
        
        if self.cache_manager:
            # Convert to serializable format
            serializable_data = [dict(p._asdict()) for p in profile_data]
            self.cache_manager.setex(cache_key, 1800, json.dumps(serializable_data))  # 30 min cache
        
        return profile_data
    
    def _index_by_post_id(self, data: List) -> Dict:
        """Helper to index data by post_seq"""
        indexed = {}
        for item in data:
            post_id = getattr(item, 'post_seq', None)
            if post_id:
                if post_id not in indexed:
                    indexed[post_id] = []
                indexed[post_id].append(item)
        return indexed
    
    def _index_reaction_counts(self, reaction_data: List) -> Dict:
        """Index reaction counts by post and reaction type"""
        indexed = {}
        for item in reaction_data:
            post_id = item.post_seq
            reaction_type = item.reaction_type
            
            if post_id not in indexed:
                indexed[post_id] = {}
            indexed[post_id][reaction_type] = {
                'count': item.count,
                'user_reacted': item.user_reacted == 'YES'
            }
        return indexed
    
    def _enrich_posts_data(self, posts: List[Dict], related_data: Dict) -> List[Dict]:
        """Enrich posts with all related data efficiently"""
        
        for post in posts:
            post_id = post['post_seq']
            profile_id = post['profile_seq']
            
            # Get profile data
            profile = related_data['profile_data'].get(profile_id)
            if profile:
                post.update({
                    'user_handle': profile.user_handle,
                    'display_name': profile.display_name,
                    'profile_picture': self._create_media_url(profile.profile_picture, 'profiles'),
                    'is_verified': profile.is_verified,
                    'profile_type': profile.type,
                    'enable_comment': profile.enable_comment,
                    'enable_watermark': profile.enable_watermark
                })
            
            # Get engagement counts
            engagement = related_data['engagement_counts'].get(post_id, [])
            if engagement:
                eng = engagement[0]  # Should be single record per post
                post.update({
                    'likes': getattr(eng, 'like_count', 0),
                    'views': getattr(eng, 'view_count', 0),
                    'shares': getattr(eng, 'share_count', 0),
                    'comments': getattr(eng, 'comment_count', 0)
                })
            
            # Get user interactions
            interactions = related_data['user_interactions'].get(post_id, [])
            post.update({
                'is_liked': 'NO',
                'is_bookmarked': 'NO',
                'is_commented': 'NO',
                'is_subscribed': 'NO'
            })
            
            for interaction in interactions:
                if hasattr(interaction, 'like_seq') and interaction.like_seq:
                    post['is_liked'] = 'YES'
                if hasattr(interaction, 'bookmark_seq') and interaction.bookmark_seq:
                    post['is_bookmarked'] = 'YES'
                if hasattr(interaction, 'comment_seq') and interaction.comment_seq:
                    post['is_commented'] = 'YES'
                if hasattr(interaction, 'subscription_seq') and interaction.subscription_seq:
                    post['is_subscribed'] = 'YES'
            
            # Get reaction data
            reactions = related_data['reaction_counts'].get(post_id, {})
            post['user_reactions'] = reactions
            
            # Format timestamps
            post['posted_on'] = self.utils.get_formatted_time_past(post['posted_on'])
            
            # Create media URLs
            post['media_file'] = self._create_media_url(post['media_file'], 'posts')
            if post.get('media_cover'):
                post['media_cover'] = self._create_media_url(post['media_cover'], 'posts')
        
        return posts
    
    def _create_media_url(self, filename: str, folder_type: str) -> str:
        """Helper to create media URLs"""
        if not filename:
            return ""
        
        # Use existing generic code logic
        from sotrueapp.application.handlers.SotrueAppGenericCode import SotrueAppGenericCode
        from sotrueapp.application.misc.SotrueAppConfig import SotrueAppConfig
        from components.AppConfigs import AppConfigs
        
        generic_code = SotrueAppGenericCode(self.connDB, self.session, self.utils, self.logger)
        
        if folder_type == 'profiles':
            return generic_code.create_media_url(
                SotrueAppConfig.profile_path + filename,
                AppConfigs.s3_profiles_folder,
                True  # s3_enabled
            )
        elif folder_type == 'posts':
            return generic_code.create_media_url(
                SotrueAppConfig.media_path + filename,
                AppConfigs.s3_posts_folder,
                True  # s3_enabled
            )
        
        return filename
    
    def _update_browsing_history_optimized(self, profile_seq: int, post_ids: List[int]):
        """Optimized browsing history update using proper database design"""
        
        # Instead of comma-separated strings, use a proper junction table
        # This would require a schema change to browsing_history_items table
        
        # For now, maintain compatibility with existing schema but optimize
        current_exclusions = self._get_browsing_exclusions(profile_seq)
        new_exclusions = current_exclusions + post_ids
        
        # Keep only last 1000 items to prevent unbounded growth
        if len(new_exclusions) > 1000:
            new_exclusions = new_exclusions[-1000:]
        
        exclusions_str = ','.join(map(str, new_exclusions))
        
        # Update database
        if current_exclusions:
            params = [exclusions_str, profile_seq]
            self.connDB.execute_prepared_stmt("sotrueappuser", "UPDATE_BROWSING_HISTORY", params)
        else:
            params = [profile_seq, exclusions_str]
            self.connDB.execute_prepared_stmt("sotrueappuser", "INSERT_BROWSING_HISTORY", params)
        
        # Update cache
        if self.cache_manager:
            cache_key = f"browsing_history:{profile_seq}"
            self.cache_manager.setex(cache_key, 60, json.dumps(new_exclusions))
    
    def _get_cached_playlist_data(self, profile_seq: int) -> List[Dict]:
        """Cache playlist data for 10 minutes"""
        cache_key = f"playlist:{profile_seq}"
        
        if self.cache_manager:
            cached_data = self.cache_manager.get(cache_key)
            if cached_data:
                return json.loads(cached_data)
        
        qparams = [profile_seq, 'ACTIVE']
        subs = {"<EXCLUDING>": ""}
        show_list = self.connDB.execute_prepared_stmt("sotrueappuser", "GET_PLAYLIST", qparams, subs=subs)
        show_list_dict = self.utils.convert_tuples_to_dicts(show_list)
        
        # Process media URLs
        for show in show_list_dict:
            if show.get('thumb_file'):
                show['thumb_file'] = self._create_media_url(show['thumb_file'], 'posts')
            if show.get('home_file'):
                show['home_file'] = self._create_media_url(show['home_file'], 'posts')
        
        if self.cache_manager:
            self.cache_manager.setex(cache_key, 600, json.dumps(show_list_dict))  # 10 min cache
        
        return show_list_dict

# Required new SQL queries for optimization:

NEW_QUERIES = """
-- Optimized main posts query with profile data
<query>
    <id>GET_POSTS_OPTIMIZED</id>
    <stmt>
        SELECT CAST(up.post_seq AS VARCHAR), up.post_comments, 
               CAST(up.posted_on AS VARCHAR), up.post_type, up.viewer_fee, 
               CAST(up.expire_on AS VARCHAR), up.status, upc.media_file, 
               upc.media_type, CAST(up.profile_seq AS VARCHAR), 
               upc.media_cover, upc.fuzzy_image, upc.s3_enabled,
               up.episode_seq, prof.user_handle, prof.display_name,
               prof.profile_picture, prof.is_verified, prof.type as profile_type
        FROM user_posts up
        JOIN user_post_content upc ON up.post_seq = upc.post_seq
        JOIN user_profile prof ON up.profile_seq = prof.profile_seq
        WHERE up.profile_seq != %s 
          AND prof.status = 'ACTIVE' 
          AND up.is_playlist = 'NO'
          AND up.status = 'ACTIVE' 
          AND up.expire_on >= %s
          AND upc.media_type LIKE %s 
          AND prof.is_verified LIKE %s
          <RESTRICTION_CLAUSE>
          <EXCLUSION_CLAUSE>
        ORDER BY up.posted_on DESC
    </stmt>
    <dbkey>sotruedb</dbkey>
    <dbschema>sotrueschema</dbschema>
</query>

-- Batch user interactions query
<query>
    <id>GET_USER_POST_INTERACTIONS_BATCH</id>
    <stmt>
        SELECT post_seq, 'like' as interaction_type, like_seq as interaction_id
        FROM post_likes 
        WHERE liked_by_profile = %s AND post_seq IN (<POST_LIST>) AND status = 'ACTIVE'
        UNION ALL
        SELECT post_seq, 'bookmark' as interaction_type, bookmark_seq as interaction_id
        FROM profile_bookmarks 
        WHERE profile_seq = %s AND post_seq IN (<POST_LIST>)
        UNION ALL
        SELECT content_seq as post_seq, 'comment' as interaction_type, comment_seq as interaction_id
        FROM post_comments 
        WHERE commented_by = %s AND content_seq IN (<POST_LIST>) AND status = 'ACTIVE'
        UNION ALL
        SELECT subscribed_post_seq as post_seq, 'subscription' as interaction_type, subscription_seq as interaction_id
        FROM post_subscriptions 
        WHERE subscribed_by = %s AND subscribed_post_seq IN (<POST_LIST>)
    </stmt>
    <dbkey>sotruedb</dbkey>
    <dbschema>sotrueschema</dbschema>
</query>

-- Batch engagement counts query
<query>
    <id>GET_POST_ENGAGEMENT_COUNTS_BATCH</id>
    <stmt>
        SELECT post_seq,
               SUM(CASE WHEN interaction_type = 'like' THEN count_val ELSE 0 END) as like_count,
               SUM(CASE WHEN interaction_type = 'view' THEN count_val ELSE 0 END) as view_count,
               SUM(CASE WHEN interaction_type = 'share' THEN count_val ELSE 0 END) as share_count,
               SUM(CASE WHEN interaction_type = 'comment' THEN count_val ELSE 0 END) as comment_count
        FROM (
            SELECT post_seq, 'like' as interaction_type, COUNT(*) as count_val
            FROM post_likes 
            WHERE post_seq IN (<POST_LIST>) AND status = 'ACTIVE' AND type = 'LIKE'
            GROUP BY post_seq
            UNION ALL
            SELECT post_seq, 'view' as interaction_type, COUNT(*) as count_val
            FROM post_views 
            WHERE post_seq IN (<POST_LIST>)
            GROUP BY post_seq
            UNION ALL
            SELECT post_seq, 'share' as interaction_type, COUNT(*) as count_val
            FROM post_shares 
            WHERE post_seq IN (<POST_LIST>)
            GROUP BY post_seq
            UNION ALL
            SELECT content_seq as post_seq, 'comment' as interaction_type, COUNT(*) as count_val
            FROM post_comments 
            WHERE content_seq IN (<POST_LIST>) AND status = 'ACTIVE' AND comment_type = 'POST'
            GROUP BY content_seq
        ) counts
        GROUP BY post_seq
    </stmt>
    <dbkey>sotruedb</dbkey>
    <dbschema>sotrueschema</dbschema>
</query>
"""