from components.AppAuditBase import AppAuditBase as a

class SotrueAppUserAuditRules(a):

    audit_rules = {
        "APP_LOGIN":{
            "audit":a.notrequired,
        },
        "GMAIL_LOGIN":{
            "audit":a.notrequired,
        },
        "APPLE_LOGIN":{
            "audit":a.notrequired,
        },
        "APP_REGISTER":{
            "audit":a.notrequired,
        },
        "SUBMIT_OTP":{
            "audit":a.notrequired,
        },
        "SUBMIT_OTP_NEW":{
            "audit":a.notrequired,
        },
        "GENERATE_OTP":{
            "audit":a.notrequired,
        },
        "GENERATE_OTP_EMAIL":{
            "audit":a.notrequired,
        },
        "VERIFY_USER_NAME":{
            "audit":a.notrequired,
        },
        "SUBMIT_OTP_EMAIL":{
            "audit":a.notrequired,
        },
        "REGISTER_USER":{
            "audit":a.notrequired,
        },
        "RESEND_OTP":{
            "audit":a.notrequired,
        },
        "GET_USER_SERVER":{
            "audit":a.notrequired,
        },
        "GET_CODE_VALUES":{
            "audit":a.notrequired,
        },
        "SEND_PASSWORD_OTP":{
            "audit":a.notrequired,
        },
        "CHECK_REMOTE_DUPLICATE_USER":{
            "audit":a.notrequired,
        },
        "SUBMIT_USER_FEEDBACK":{
            "audit":a.notrequired,
        },
        "SEND_EMAIL":{
            "audit":a.notrequired,
        },
        "SUBMIT_PASSWORD_OTP":{
            "audit":a.required,                    
            "key_column":"user_seq",
            "table":"user_master",
            "input":"user_seq",
            "column":"user_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "SET_NEW_PASSWORD":{
            "audit":a.required,                    
            "key_column":"user_seq",
            "table":"user_master",
            "input":"user_seq",
            "column":"user_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "CHANGE_PASSWORD":{
            "audit":a.required,                    
            "key_column":"user_seq",
            "table":"user_master",
            "input":"user_seq",
            "column":"user_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },       
        "GET_GENRE_TOPIC_MASTERS":{
            "audit":a.notrequired,
        },   
        "SUBMIT_POST":{
            "audit":a.required,                    
            "table":"user_posts",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "DELETE_POST":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,
        },
        "GET_POSTS_USER":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_USER_POST":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_POSTS":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "SUBMIT_POST_LIKE":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "SUBMIT_POST_VIEW":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "SUBMIT_POST_COMMENT":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "UPDATE_POST_COMMENT":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.create,
        },
        "REMOVE_POST_LIKE":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,
        },
        "REMOVE_POST_COMMENT":{
            "audit":a.required,                    
            "key_column":"comment_seq",
            "table":"post_comments",
            "input":"post_seq",
            "column":"content_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,            
        },
        "DELETE_USER_POST_COMMENT":{
            "audit":a.required,                    
            "key_column":"comment_seq",
            "table":"post_comments",
            "input":"post_seq",
            "column":"content_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,            
        },
        "SUBMIT_COMMENT_LIKE":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "REMOVE_COMMENT_LIKE":{
            "audit":a.required,                    
            "key_column":"comment_seq",
            "table":"post_comment_likes",
            "input":"comment_seq",
            "column":"comment_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,
        },
        "GET_POST_COMMENTS":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "SUBMIT_COMMENT_COMMENT":{
            "audit":a.required,                    
            "key_column":"content_seq",
            "table":"post_comments",
            "input":"comment_seq",
            "column":"content_seq",
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "REMOVE_COMMENT_COMMENT":{
            "audit":a.required,                    
            "key_column":"comment_seq",
            "table":"post_comments",
            "input":"comment_seq",
            "column":"content_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,
        },
        "SAVE_USER_PROFILE":{
            "audit":a.required,                    
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_USER_PROFILE":{
            "audit":a.required,                    
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "SAVE_ACCOUNT_SETTING":{
            "audit":a.required,                    
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_ACCOUNT_SETTING":{
            "audit":a.required,                    
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "FOLLOW_PROFILE":{
            "audit":a.required,                    
            "table":"profile_follower",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "UNFOLLOW_PROFILE":{
            "audit":a.required,                    
            "key_column":"follower_seq",
            "table":"profile_follower",
            "input":"un_profile_seq",
            "column":"following_profile_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,
        },
        "GET_FOLLOWERS":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_follower",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_FOLLOWER_COUNT":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_follower",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_FOLLOWING":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_follower",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_FOLLOWING_COUNT":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_follower",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "CHECK_USER_FOLLOWING":{
            "audit":a.required,                    
            "key_column":"follower_seq",
            "table":"profile_follower",
            "input":"profile_seq",
            "column":"following_profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "ADD_BOOKMARK":{
            "audit":a.required,                    
            "table":"profile_bookmarks",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "REMOVE_BOOKMARK":{
            "audit":a.required,                    
            "key_column":"bookmark_seq",
            "table":"profile_bookmarks",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,
        },
        "GET_BOOKMARKS":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_bookmarks",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_BOOKMARK_COUNT":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_bookmarks",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PROFILE_SUGGESTIONS":{
            "audit":a.required,                    
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "SUBMIT_STORY":{
            "audit":a.required,                    
            "table":"user_stories",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "GET_STORIES":{
            "audit":a.required,                    
            "key_column":None,
            "table":"user_stories",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "DELETE_STORY":{
            "audit":a.required,                    
            "key_column":"story_seq",
            "table":"user_stories",
            "input":"story_seq",
            "column":"story_seq",
            "function":"USER_ACTION_DELETE",
            "action":a.delete,
        },
        "GET_PROFILE_STORIES":{
            "audit":a.required,                    
            "key_column":"profile_seq",
            "table":"user_stories",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_MEDIA_DATA":{
            "audit":a.notrequired,
        },
        "GET_MEDIA_URL_POST":{
            "audit":a.required,
            "key_column":"post_seq",
            "table":"user_posts",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_MEDIA_URL_PROFILE":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PRIVACY_SETTINGS":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "UPDATE_PRIVACY_SETTINGS":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_MEDIA_COUNT":{
            "audit":a.required,
            "key_column":None,
            "table":"user_posts",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "DO_SEARCH":{
            "audit":a.required,
            "key_column":None,
            "table":"user_profile",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "DO_TOPIC_SEARCH":{
            "audit":a.required,
            "key_column":None,
            "table":"user_profile",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "DO_PLAYLIST_SEARCH":{
            "audit":a.required,
            "key_column":None,
            "table":"user_profile",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "TYPE_AHEAD_SEARCH":{
            "audit":a.required,
            "key_column":None,
            "table":"user_profile",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PAYMENT_ORDER":{
            "audit":a.required,                    
            "table":"gateway_payment_init",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "GET_PAYMENT_HASH":{
            "audit":a.required,                    
            "table":"gateway_payment_init",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "UPDATE_PAYMENT_ORDER":{
            "audit":a.required,
            "key_column":"init_seq",
            "table":"gateway_payment_init",
            "input":"init_seq",
            "column":"init_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_PAY_ORDER_STATUS":{
            "audit":a.notrequired,
        },
        "PAYU_PAYMENT":{
            "audit":a.notrequired,
        },
         "GET_PAYMENT_STATUS":{
            "audit":a.required,
            "key_column":"init_seq",
            "table":"gateway_payment_init",
            "input":"init_seq",
            "column":"init_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },  
        "GET_PROFILE_BALANCE":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },                
        "GET_SUBSCRIBE_DETAILS":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"subscribe_profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "SUBSCRIBE_PROFILE":{
            "audit":a.required,                    
            "table":"profile_subscription",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "SUBSCRIBE_POST":{
            "audit":a.required,                    
            "table":"post_subscription",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "GET_SUBSCRIBED_PROFILES":{
            "audit":a.required,
            "key_column":"subscribed_by_seq",
            "table":"profile_subscription",
            "input":"profile_seq",
            "column":"subscribed_by_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_SUBSCRIBED_POSTS":{
            "audit":a.required,
            "key_column":"subscribed_by_seq",
            "table":"post_subscription",
            "input":"profile_seq",
            "column":"subscribed_by_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "UNSUBSCRIBE_PROFILE":{
            "audit":a.required,
            "key_column":"subscription_seq",
            "table":"profile_subscription",
            "input":"subscription_seq",
            "column":"subscription_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_PAYMENTS_CREDIT":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_payments",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PAYMENTS_DEBIT":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_charges",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
         "GET_USER_POST_SUBSCRIBERS":{
            "audit":a.required,
            "key_column":None,
            "table":"post_subscription",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_USER_PROFILE_SUBSCRIBERS":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_subscription",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_SUBSCRIBER_COUNT":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_subscription",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_USER_PROFILE_SUBSCRIBER_PAID":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_subscription",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "REPORT_ISSUE":{
            "audit":a.required,                    
            "table":"post_reports",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "DELETE_ACCOUNT":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "UPLOAD_ACCOUNT_VERIFICATION":{
            "audit":a.required,                    
            "table":"profile_verification",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "UPLOAD_ACCOUNT_VERIFICATION_FIRM":{
            "audit":a.required,                    
            "table":"profile_verification",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "RESTRICT_ACCOUNT":{
            "audit":a.required,                    
            "table":"restricted_profiles",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "BLOCK_ACCOUNT":{
            "audit":a.required,                    
            "table":"restricted_profiles",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "GET_RESTRICTED_ACCOUNTS":{
            "audit":a.required,
            "key_column":None,
            "table":"restricted_profiles",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_BLOCKED_ACCOUNTS":{
            "audit":a.required,
            "key_column":None,
            "table":"restricted_profiles",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "UNBLOCK_ACCOUNT":{
            "audit":a.required,
            "key_column":"restrict_seq",
            "table":"restricted_profiles",
            "input":"restrict_seq",
            "column":"restrict_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "UNRESTRICT_ACCOUNT":{
            "audit":a.required,
            "key_column":"restrict_seq",
            "table":"restricted_profiles",
            "input":"restrict_seq",
            "column":"restrict_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_NOTIFICATIONS_LIKES":{
            "audit":a.required,
            "key_column":None,
            "table":"post_likes",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_NOTIFICATIONS_COMMENTS":{
            "audit":a.required,
            "key_column":None,
            "table":"post_comments",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_NOTIFICATIONS_SUBSCRIBES":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_subscription",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_NOTIFICATIONS_FOLLOWS":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_follower",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
         "GET_NOTIFICATIONS_TAG":{
            "audit":a.required,
            "key_column":None,
            "table":"post_tags",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PAYMENT_SUMMARY_VALUES":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_charges",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_ALERT_SETTINGS":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "UPDATE_ALERT_SETTINGS":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_USER_MESSAGES":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile_alert",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "UPDATE_MESSAGE_STATUS":{
            "audit":a.required,
            "key_column":"alert_seq",
            "table":"user_profile_alert",
            "input":"alert_seq",
            "column":"alert_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "CHECK_USER_HANDLE":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "UPDATE_USER_LOCATION":{
            "audit":a.required,                    
            "key_column":"user_seq",
            "table":"user_master",
            "input":"user_seq",
            "column":"user_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_USER_REFERRAL_CODE":{
            "audit":a.required,
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_REFERRAL_EARNED_USERS":{
            "audit":a.required,
            "key_column":None,
            "table":"user_referral",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PAYOUT_DETAILS":{
            "audit":a.required,
            "key_column":None,
            "table":"referral_earning",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PAYOUT_HISTORY":{
            "audit":a.required,
            "key_column":None,
            "table":"user_payout_log",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "SUBMIT_PAYOUT_REQUEST":{
            "audit":a.required,                    
            "table":"user_payout_log",
            "input":"_last_seq",
            "column":a.session,
            "function":"USER_ACTION_CREATE",
            "action":a.create,
        },
        "GET_POST_LIKE_USERS":{
            "audit":a.required,
            "key_column":None,
            "table":"post_likes",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "SEARCH_POST_LIKE_USERS":{
            "audit":a.required,
            "key_column":None,
            "table":"post_likes",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "SAVE_FCM_KEY":{
            "audit":a.required,                    
            "key_column":"user_seq",
            "table":"user_master",
            "input":"user_id",
            "column":"email_id",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },   
        "GET_USER_SEQ":{
            "audit":a.required,
            "key_column":None,
            "table":"user_profile",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_TAGGED_USERS":{
            "audit":a.required,
            "key_column":None,
            "table":"post_tags",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_POSTS_TAGGED":{
            "audit":a.required,
            "key_column":None,
            "table":"post_tags",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_USER_TO_TAG":{
            "audit":a.required,
            "key_column":None,
            "table":"user_master",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "VALIDATE_TAGGING":{
            "audit":a.required,
            "key_column":None,
            "table":"user_master",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_STORY_DETAILS":{
            "audit":a.required,                    
            "key_column":"story_seq",
            "table":"user_stories",
            "input":"story_seq",
            "column":"story_seq",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "UPDATE_NOTIFICATION_VIEW":{
            "audit":a.required,
            "key_column":None,
            "table":"notification_view",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_NOTIFICATION_STATUS":{
            "audit":a.required,
            "key_column":None,
            "table":"notification_view",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_FINANCIAL_REPORT":{
            "audit":a.required,
            "key_column":None,
            "table":"notification_view",
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_POPULAR_CATEGORIES":{
            "audit":a.required,
            "key_column":None,
            "table":"profile_category",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_LANDING_PROFILES":{
            "audit":a.notrequired,
         },
        "UPDATE_SHARE_COUNT":{
            "audit":a.required,                    
            "key_column":"post_seq",
            "table":"post_shares",
            "input":"post_seq",
            "column":"post_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "UPDATE_VERIFY_NOTIFY":{
            "audit":a.required,                    
            "key_column":"profile_seq",
            "table":"user_profile",
            "input":"profile_seq",
            "column":"profile_seq",
            "function":"USER_ACTION_UPDATE",
            "action":a.update,
        },
        "GET_INTEREST_LIST":{
            "audit":a.required,
            "key_column":None,
            "table":"interest_master",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_INTEREST_CATEGORIES":{
            "audit":a.required,
            "key_column":None,
            "table":"interest_master",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_USER_INTERESTS":{
            "audit":a.required,
            "key_column":None,
            "table":"user_interests",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "UPDATE_USER_INTERESTS":{
            "audit":a.notrequired,
        },
        "UPDATE_USER_UI_COLOUR":{
            "audit":a.notrequired,
        },
        "GET_LOCATION_LIST":{
            "audit":a.required,
            "key_column":None,
            "table":"user_posts",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PLAYLIST_SHOWS":{
            "audit":a.required,
            "key_column":None,
            "table":"show_master",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PLAYLIST_EPISODES":{
            "audit":a.required,
            "key_column":None,
            "table":"episode_master",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },
        "GET_PLAYLIST_CLIPS":{
            "audit":a.required,
            "key_column":None,
            "table":"user_posts",                    
            "function":"USER_ACTION_READ",
            "action":a.read,
        },        
    }
