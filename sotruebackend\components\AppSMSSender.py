import requests
import json

from .AppLogger import AppLogger
from .AppConfigs import AppConfigs

class AppSMSSender:

    def __init__():
        pass

    #
    # Send an SMS
    #
    @staticmethod
    def send_sms(mobile, message):                
        AppSMSSender.send_sms_prod(mobile,message)        

    #
    # Send an SMS For Prod
    #
    @staticmethod
    def send_sms_prod(mobile, message):        
        if not message.startswith("91") and len(mobile) <= 10:
            mobile = "91" + mobile

        url = "https://restapi.smscountry.com/v0.1/Accounts/kHcvHNxvWDgSH8ZkHk6J/SMSes/"
        headers = {
          'Authorization': 'Basic a0hjdkhOeHZXRGdTSDhaa0hrNko6MVllQWlaN2tCcVgwbnhlRUJUenpDTHJCdHBVMFBQd0tvakhBcllibA==',
          'Content-Type': 'application/json'}
        payload = json.dumps({
          "Text": message,
          "Number": mobile,
          "SenderId": "SOTRUE",
          "Tool": "API",          
        })        
        try:
            resp = requests.request("POST", url, headers=headers, data=payload)
            print(f"[SMSCountry API] Status: {resp.status_code}, Response: {resp.text}")
            AppLogger.log_message(f"SMSCountry API response: {resp.status_code} {resp.text}", "DEBUG")
            resp.raise_for_status()
        except Exception as e:
            print(f"[SMSCountry API] ERROR: {str(e)}")
            AppLogger.log_message(f"SMSCountry API error: {str(e)}", "ERROR")
        return(resp.text)

    #
    # Send an SMS For Dev
    #
    @staticmethod
    def send_sms_dev(mobile, message):
       
        url = "http://api.msg91.com/api/sendhttp.php"

        if len(str(mobile)) == 10:
            mobile = "91" + str(mobile)
            
        params = {'authkey':"106060AbJc2YWHchj56d1254b",
                  'mobiles': mobile,
                  'message':message,
                  'sender':"MSGIND",
                  'route':4,
                  'country':91}

        #AppLogger.log_message("Sending Message: <" + message + "> to <" + str(mobile),"DEBUG")
        try:
            resp = requests.get(url = url, params = params)
            print(f"[MSG91 API] Status: {resp.status_code}, Response: {resp.text}")
            AppLogger.log_message(f"MSG91 API response: {resp.status_code} {resp.text}", "DEBUG")
            resp.raise_for_status()
        except Exception as e:
            print(f"[MSG91 API] ERROR: {str(e)}")
            AppLogger.log_message(f"MSG91 API error: {str(e)}", "ERROR")
        return resp.text
