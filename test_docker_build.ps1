# Test script to build and test the Docker image locally
Write-Host "Building Docker image..." -ForegroundColor Yellow
docker build -t sotrue-backend-test .

if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Docker build successful" -ForegroundColor Green
    
    Write-Host "Testing imports in container..." -ForegroundColor Yellow
    docker run --rm sotrue-backend-test python /app/test_imports.py
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ All tests passed!" -ForegroundColor Green
    } else {
        Write-Host "✗ Import tests failed" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✗ Docker build failed" -ForegroundColor Red
    exit 1
}
