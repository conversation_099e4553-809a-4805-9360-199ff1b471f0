#!/usr/bin/env python3
"""
Quick test to verify CSRF fix is working
"""

import requests
import time

def test_csrf_fix():
    """Test if CSRF fix is working"""
    print("🧪 Testing CSRF Fix")
    print("=" * 40)
    
    base_url = "http://localhost:8000"
    
    # Test cases
    test_cases = [
        {
            "name": "GENERATE_OTP (the failing one)",
            "data": {"_action_code": "GENERATE_OTP", "mobile": "9420346490"}
        },
        {
            "name": "GET_CODE_VALUES", 
            "data": {"_action_code": "GET_CODE_VALUES"}
        }
    ]
    
    csrf_fixed = True
    
    for test_case in test_cases:
        print(f"\n📡 Testing {test_case['name']}...")
        try:
            url = f"{base_url}/sotrueapp/appservice"
            response = requests.post(url, data=test_case['data'], timeout=10)
            
            print(f"   Status: {response.status_code}")
            
            if response.status_code == 403 and "CSRF" in response.text:
                print(f"   ❌ CSRF ERROR STILL EXISTS!")
                print(f"   Response: {response.text[:150]}")
                csrf_fixed = False
            elif response.status_code in [200, 400, 500]:
                print(f"   ✅ CSRF FIXED! No more CSRF errors")
                try:
                    result = response.json()
                    print(f"   📊 API Status: {result.get('status', 'Unknown')}")
                except:
                    print(f"   📊 Non-JSON response (normal)")
            else:
                print(f"   ⚠️  Status: {response.status_code}")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Cannot connect - is Docker running?")
            return False
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    if csrf_fixed:
        print(f"\n🎉 SUCCESS! CSRF issue is FIXED!")
        print("✅ No more 'Forbidden (CSRF cookie not set.)' errors")
    else:
        print(f"\n❌ CSRF issue still exists")
    
    return csrf_fixed

if __name__ == "__main__":
    # Wait a moment for server to be ready
    print("⏳ Waiting for server...")
    time.sleep(2)
    
    success = test_csrf_fix()
    
    if success:
        print("\n🚀 Your API is now working without CSRF errors!")
    else:
        print("\n🔧 Need to rebuild Docker containers with the fix")
        print("Run: docker-compose down && docker-compose build --no-cache && docker-compose up -d")
