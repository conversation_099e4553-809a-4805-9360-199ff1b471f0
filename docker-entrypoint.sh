#!/bin/bash
set -e

# Create log directory if it doesn't exist
mkdir -p /tmp/sotruelogs

# Wait for the database to be ready
echo "Waiting for database to be ready..."
while ! pg_isready -h $TEST_DB_HOST -p $TEST_DB_PORT -U $TEST_DB_USER
do
  echo "Database is not ready - sleeping"
  sleep 2
done
echo "Database is ready!"

# Apply database migrations
echo "Applying database migrations..."
cd sotruebackend
python manage.py migrate
cd ..

# Start the application
echo "Starting application..."
exec "$@"
