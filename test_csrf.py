#!/usr/bin/env python3
"""
Test script to verify CSRF exemption is working
"""

import requests
import json

def test_csrf_exemption():
    """Test that API endpoints work without CSRF tokens"""
    
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        "/sotrueapp/appservice",
        "/sotrueapp/adminservice", 
        "/sotrueapp/playlist"
    ]
    
    print("🔧 Testing CSRF Exemption")
    print("=" * 50)
    
    for endpoint in endpoints:
        url = base_url + endpoint
        print(f"\n📡 Testing: {url}")
        
        try:
            # Test GET request
            response = requests.get(url, timeout=10)
            print(f"   GET Status: {response.status_code}")
            
            if response.status_code == 403 and "CSRF" in response.text:
                print("   ❌ CSRF protection still active")
            elif response.status_code in [200, 400, 405]:  # 405 = Method not allowed is OK
                print("   ✅ CSRF exemption working")
            else:
                print(f"   ⚠️  Unexpected status: {response.status_code}")
            
            # Test POST request with simple data
            test_data = {"_action_code": "GET_CODE_VALUES"}
            response = requests.post(url, data=test_data, timeout=10)
            print(f"   POST Status: {response.status_code}")
            
            if response.status_code == 403 and "CSRF" in response.text:
                print("   ❌ CSRF protection still active for POST")
            elif response.status_code in [200, 400, 500]:  # Various OK responses
                print("   ✅ POST CSRF exemption working")
            else:
                print(f"   ⚠️  Unexpected POST status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ Connection error: {e}")
    
    print(f"\n🎯 Testing specific API call...")
    
    # Test the specific API call that was failing
    print(f"\n🎯 Testing GENERATE_OTP API call...")
    try:
        url = base_url + "/sotrueapp/appservice"
        data = {
            "_action_code": "GENERATE_OTP",
            "mobile": "9420346490"
        }

        response = requests.post(url, data=data, timeout=10)
        print(f"   GENERATE_OTP Status: {response.status_code}")

        if response.status_code == 403 and "CSRF" in response.text:
            print("   ❌ CSRF still blocking API calls")
            print("   📝 Response:", response.text[:200])
        else:
            print("   ✅ API call not blocked by CSRF")
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"   📊 API Response: {result.get('status', 'Unknown')}")
                    if 'data' in result:
                        print(f"   📊 Data: {result['data']}")
                except:
                    print("   📊 API returned non-JSON response")
                    print(f"   📝 Raw response: {response.text[:100]}")
            elif response.status_code == 500:
                print("   ⚠️  Server error (but CSRF is working)")
                print(f"   📝 Response: {response.text[:200]}")

    except requests.exceptions.RequestException as e:
        print(f"   ❌ API test failed: {e}")

    # Test another API call
    print(f"\n🎯 Testing GET_CODE_VALUES API call...")
    try:
        url = base_url + "/sotrueapp/appservice"
        data = {
            "_action_code": "GET_CODE_VALUES"
        }

        response = requests.post(url, data=data, timeout=10)
        print(f"   GET_CODE_VALUES Status: {response.status_code}")

        if response.status_code == 403 and "CSRF" in response.text:
            print("   ❌ CSRF still blocking API calls")
        else:
            print("   ✅ CSRF exemption working for GET_CODE_VALUES")

    except requests.exceptions.RequestException as e:
        print(f"   ❌ API test failed: {e}")
    
    print("\n" + "=" * 50)
    print("🏁 CSRF Test Complete")

if __name__ == "__main__":
    test_csrf_exemption()
