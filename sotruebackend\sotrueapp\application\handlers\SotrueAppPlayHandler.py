from components.AppProcessHandlerBase import AppProcessHandlerBase
from components.AppSecurity import AppSecurity
from components.AppConfigs import AppConfigs
from components.AppEmail import AppEmail
from components.AppSMSSender import App<PERSON><PERSON>ender
from components.AppRazorPay import AppRazorPay
from components.AppExcel import AppExcel
from components.AppAwsS3 import AppAwsS3
from .SotrueAppGenericCode import SotrueAppGenericCode
from sotrueapp.application.misc.SotrueAppConfig import SotrueAppConfig

import os

class SotrueAppPlayHandler(AppProcessHandlerBase):
    
    def handle_login(self):
        qparams = [self.params["user_id"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_USER_CREDENTIALS_EMAIL",qparams)
        if not user_data:
            user_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_USER_CREDENTIALS_MOBILE",qparams)

        if user_data:
            # if user_data[0].playlist_approved == "YES":
            #     response = self.create_error("AE019")
            #     return(response)  

            if user_data[0].status != 'ACTIVE':
                response = self.create_error("AE001")
                return(response)    
            
            if user_data[0].playlist_password == self.params["password"]:   
                qparams = [user_data[0].user_seq,'ACTIVE']
                profile_seq = self.connDB.execute_prepared_stmt("sotrueappplay","GET_PROFILE_FOR_USER",qparams)

                response = dict(user_seq=user_data[0].user_seq,profile_seq=profile_seq[0].profile_seq)

                session_data = {"_user_seq":user_data[0].user_seq, "_profile_seq":profile_seq[0].profile_seq,
                                "_email_id":user_data[0].email_id,
                                "_session_expiry":AppConfigs.session_expiry}
                self.session.start_user_session(session_data)
                
                response = self.create_response(response,"AE002")
                return(response)
            else:
                response = self.create_error("AE020")
                return(response)            
        else:
            response = self.create_error("AE021")
            return(response)  
    

    def generate_login_otp(self):
        qparams = [self.params["user_id"]]
        user_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_USER_CREDENTIALS_EMAIL",qparams)
        if not user_data:
            user_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_USER_CREDENTIALS_MOBILE",qparams)
        if user_data:
            otp = AppSecurity.get_random_otp(4)
            qparams = [otp,user_data[0].user_seq]
            self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_USER_CREDENTIALS",qparams)

            if user_data[0].mobile_number:
                AppSMSSender.send_sms(user_data[0].mobile_number,"Hey Hey! Use this One Time Password (OTP) " + str(otp) + " to log in to your SoTrue Playlist account. This OTP will be valid for the next 5 minutes - SOTRUE")

            # if user_data[0].email_id:
            #     mailer = AppEmail()
            #     mailer.send_email(user_data[0].email_id,None,"Your OTP for Playlist Login",
            #         "Dear " + user_data[0].full_name + "\n\n, Your One-Time Password (OTP) for playlist login is " + str(otp) + ". Please use this code to complete your login.\n\nImportant:\n\n-This code is valid for a limited time.\n-Do not share this OTP with anyone.\n-If you did not request this code, please contact our support team immediately.\n\nThank you,\nSoTrue Team");                    
            response = self.create_response(dict(),"AE018")
            return(response)
        else:
            response = self.create_error("AE003")
            return(response)  


    def add_show(self):
        
        if self.params["uploads"] is None:        
            response = self.create_error("AE012")
            return(response)

        logo_image = None
        thumb_image = None
        preview_image = None
        master_image = None
        home_image = None
        banner_image = None

        count = 0
        self.logger.log_message(self.params["uploads"])
        while count < len(self.params["uploads"]):
            if self.params["uploads"][count]["key"] == "logo_file":
                logo_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "thumb_file":
                thumb_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "preview_file":
                preview_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "master_file":
                master_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "home_file":
                home_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "banner_file":
                banner_image = self.params["uploads"][count]["upload_name"]
            count+=1        

        #if not logo_image or not thumb_image:
        #    response = self.create_error("AE012")
        #    return(response)

        if logo_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + logo_image)
            logo_image = logo_image[0:logo_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + logo_image[logo_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + logo_image, file_name, AppConfigs.s3_posts_folder)
                logo_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + logo_image,SotrueAppConfig.media_path + logo_image)            
                self.cache_uploaded_file("logo_image",SotrueAppConfig.media_path + logo_image)
            self.utils.delete_file(self.configs.temp_folder_path + logo_image)

        if thumb_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + thumb_image)
            thumb_image = thumb_image[0:thumb_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + thumb_image[thumb_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + thumb_image, file_name, AppConfigs.s3_posts_folder)
                thumb_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + thumb_image,SotrueAppConfig.media_path + thumb_image)            
                self.cache_uploaded_file("thumb_image",SotrueAppConfig.media_path + thumb_image)
            self.utils.delete_file(self.configs.temp_folder_path + thumb_image)

        if preview_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + preview_image)
            preview_image = preview_image[0:preview_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + preview_image[preview_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + preview_image, file_name, AppConfigs.s3_posts_folder)
                preview_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + preview_image,SotrueAppConfig.media_path + preview_image)            
                self.cache_uploaded_file("preview_image",SotrueAppConfig.media_path + preview_image)
            self.utils.delete_file(self.configs.temp_folder_path + preview_image)

        if home_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + home_image)
            home_image = home_image[0:home_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + home_image[home_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + home_image, file_name, AppConfigs.s3_posts_folder)
                home_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + home_image,SotrueAppConfig.media_path + home_image)            
                self.cache_uploaded_file("home_image",SotrueAppConfig.media_path + home_image)
            self.utils.delete_file(self.configs.temp_folder_path + home_image)

        if banner_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + banner_image)
            banner_image = banner_image[0:banner_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + banner_image[banner_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + banner_image, file_name, AppConfigs.s3_posts_folder)
                banner_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + banner_image,SotrueAppConfig.media_path + banner_image)            
                self.cache_uploaded_file("banner_image",SotrueAppConfig.media_path + banner_image)
            self.utils.delete_file(self.configs.temp_folder_path + banner_image)

        if master_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + master_image)
            master_image = master_image[0:master_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + master_image[master_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + master_image, file_name, AppConfigs.s3_posts_folder)
                master_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + master_image,SotrueAppConfig.media_path + master_image)            
                self.cache_uploaded_file("master_image",SotrueAppConfig.media_path + master_image)
            self.utils.delete_file(self.configs.temp_folder_path + master_image)        

        qparams = [self.params["title"],self.params["description"],self.params["is_paid"],
                    self.params["price"] if "price" in self.params else None,
                    self.params["location"] if "location" in self.params else None,
                    self.params["expiry_date"] if "expiry_date" in self.params else '9999-12-31',
                    self.params["schedule_on"] if "schedule_on" in self.params else self.utils.get_cur_timestamp(),
                    logo_image, thumb_image, preview_image, home_image, banner_image, 'ACTIVE', 
                    self.session.get_session_value("_user_seq"),
                    'YES' if AppConfigs.s3_enabled else 'NO',
                    self.params["release_year"] if "release_year" in self.params else None,
                    self.params["grid_title"]]
        show_seq = self.connDB.execute_prepared_stmt("sotrueappplay","INSERT_SHOW",qparams)

        if master_image:
            qparams = [master_image,self.session.get_session_value("_user_seq")]
            self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_PLAYLIST_MASTER",qparams)

        if "topics" in self.params:
            topics = self.utils.parse_json(self.params["topics"])
            for topic in topics:
                qparams = [show_seq,topic]
                show_topic_seq = self.connDB.execute_prepared_stmt("sotrueappplay","INSERT_SHOW_TOPIC",qparams)

        response = self.create_response(dict(show_seq=show_seq),"AE013")
        self.session.set_session_value("_last_seq",show_seq)
        return(response)


    def get_show(self):
        qparams = [self.params["show_seq"]]
        show_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_SHOW_DETAILS",qparams)
        if not show_data:
            response = self.create_error("AE005")
            return(response)     
        show_topics = self.connDB.execute_prepared_stmt("sotrueappplay","GET_SHOW_TOPICS",qparams)
        topics = []
        for topic in show_topics:
            topics.append(topic)

        show_data = self.utils.convert_tuples_to_dicts(show_data)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        show_data[0]["logo_file"] = show_data[0]["logo_file"] if not show_data[0]["logo_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show_data[0]["logo_file"],AppConfigs.s3_posts_folder,show_data[0]["s3_enabled"])
        show_data[0]["thumb_file"] = show_data[0]["thumb_file"] if not show_data[0]["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show_data[0]["thumb_file"],AppConfigs.s3_posts_folder,show_data[0]["s3_enabled"])
        show_data[0]["preview_file"] = show_data[0]["preview_file"] if not show_data[0]["preview_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show_data[0]["preview_file"],AppConfigs.s3_posts_folder,show_data[0]["s3_enabled"])
        show_data[0]["home_file"] = show_data[0]["home_file"] if not show_data[0]["home_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show_data[0]["home_file"],AppConfigs.s3_posts_folder,show_data[0]["s3_enabled"])
        show_data[0]["banner_file"] = show_data[0]["banner_file"] if not show_data[0]["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + show_data[0]["banner_file"],AppConfigs.s3_posts_folder,show_data[0]["s3_enabled"])
        
        show_data[0]["topics"] = topics
        response = self.create_response(show_data,"AE004")
        return(response)


    def update_show(self):
        qparams = [self.params["show_seq"]]
        show_details = self.connDB.execute_prepared_stmt("sotrueappplay","GET_SHOW_DETAILS",qparams)
        if not show_details:
            response = self.create_error("AE005")
            return(response)

        #if show_details[0].scheduled_on and self.utils.get_cur_timestamp()>str(show_details[0].scheduled_on):
        #    response = self.create_error("AE014")
        #    return(response)

        logo_image = None
        thumb_image = None
        preview_image = None
        master_image = None
        home_image = None
        banner_image = None

        count = 0
        while self.params["uploads"] is not None and count < len(self.params["uploads"]):
            if self.params["uploads"][count]["key"] == "logo_file":
                logo_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "thumb_file":
                thumb_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "preview_file":
                preview_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "master_file":
                master_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "home_file":
                home_image = self.params["uploads"][count]["upload_name"]
            elif self.params["uploads"][count]["key"] == "banner_file":
                banner_image = self.params["uploads"][count]["upload_name"]
            count+=1        
            
        if logo_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + logo_image)
            logo_image = logo_image[0:logo_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + logo_image[logo_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + logo_image, file_name, AppConfigs.s3_posts_folder)
                logo_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + logo_image,SotrueAppConfig.media_path + logo_image)            
                self.cache_uploaded_file("logo_image",SotrueAppConfig.media_path + logo_image)
            self.utils.delete_file(self.configs.temp_folder_path + logo_image)

        if thumb_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + thumb_image)
            thumb_image = thumb_image[0:thumb_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + thumb_image[thumb_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + thumb_image, file_name, AppConfigs.s3_posts_folder)
                thumb_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + thumb_image,SotrueAppConfig.media_path + thumb_image)            
                self.cache_uploaded_file("thumb_image",SotrueAppConfig.media_path + thumb_image)
            self.utils.delete_file(self.configs.temp_folder_path + thumb_image)

        if preview_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + preview_image)
            preview_image = preview_image[0:preview_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + preview_image[preview_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + preview_image, file_name, AppConfigs.s3_posts_folder)
                preview_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + preview_image,SotrueAppConfig.media_path + preview_image)            
                self.cache_uploaded_file("preview_image",SotrueAppConfig.media_path + preview_image)
            self.utils.delete_file(self.configs.temp_folder_path + preview_image)


        if home_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + home_image)
            home_image = home_image[0:home_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + home_image[home_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + home_image, file_name, AppConfigs.s3_posts_folder)
                home_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + home_image,SotrueAppConfig.media_path + home_image)            
                self.cache_uploaded_file("home_image",SotrueAppConfig.media_path + home_image)
            self.utils.delete_file(self.configs.temp_folder_path + home_image)


        if banner_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + banner_image)
            banner_image = banner_image[0:banner_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + banner_image[banner_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + banner_image, file_name, AppConfigs.s3_posts_folder)
                banner_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + banner_image,SotrueAppConfig.media_path + banner_image)            
                self.cache_uploaded_file("banner_image",SotrueAppConfig.media_path + banner_image)
            self.utils.delete_file(self.configs.temp_folder_path + banner_image)

        if master_image:
            resize_extn = self.utils.resize_image(self.configs.temp_folder_path + master_image)
            master_image = master_image[0:master_image.rfind(".")+1] + resize_extn
            if AppConfigs.s3_enabled:
                file_name = self.utils.get_uuid_str() + master_image[master_image.rfind("."):]
                awsS3 = AppAwsS3()
                awsS3.send_media_file(self.configs.temp_folder_path + master_image, file_name, AppConfigs.s3_posts_folder)
                master_image = file_name
            else:
                self.utils.copy_files(self.configs.temp_folder_path + master_image,SotrueAppConfig.media_path + master_image)            
                self.cache_uploaded_file("master_image",SotrueAppConfig.media_path + master_image)
            self.utils.delete_file(self.configs.temp_folder_path + master_image)

        qparams = [self.params["title"],self.params["description"],
                   self.params["price"] if "price" in self.params else None,
                    self.params["location"] if "location" in self.params else None,
                    self.params["expiry_date"] if "expiry_date" in self.params else '9999-12-31',
                    self.params["schedule_on"] if "schedule_on" in self.params else self.utils.get_cur_timestamp(),
                    logo_image if logo_image else show_details[0].logo_file,
                    thumb_image if thumb_image else show_details[0].thumb_file,
                    preview_image if preview_image else show_details[0].preview_file,
                    home_image if home_image else show_details[0].home_file,
                    banner_image if banner_image else show_details[0].banner_file,
                    self.params["release_year"] if "release_year" in self.params else None,
                    self.params["grid_title"], self.params["show_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_SHOW",qparams)

        qparams = ['PENDING',None,None,None,self.params["show_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","RESET_SHOW_APPROVAL",qparams)

        if master_image:
            qparams = [master_image,self.session.get_session_value("_user_seq")]
            self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_PLAYLIST_MASTER",qparams)

        if "topics" in self.params:
            qparams = [self.params["show_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappplay","DELETE_SHOW_TOPIC",qparams)
            topics = self.utils.parse_json(self.params["topics"])
            for topic in topics:
                qparams = [self.params["show_seq"],topic]
                show_topic_seq = self.connDB.execute_prepared_stmt("sotrueappplay","INSERT_SHOW_TOPIC",qparams)

        response = self.create_response(dict(),"AE006")
        return(response)


    def set_show_status(self):
        qparams = [self.params["status"],self.params["show_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_SHOW_STATUS",qparams)

        qparams = [self.params["show_seq"],'ACTIVE']
        seasons = self.connDB.execute_prepared_stmt("sotrueappplay","GET_ALL_SEASONS",qparams)
        if seasons:
            for rec in seasons:
                qparams = ["INACTIVE",rec.season_seq]
                self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_SEASON_STATUS",qparams)

        qparams = [self.params["show_seq"],'ACTIVE']
        subs = {"<SEASON_QUERY>":""}
        episode_details = self.connDB.execute_prepared_stmt("sotrueappplay","GET_ALL_EPISODES",qparams,subs=subs)
        if episode_details:
            for rec in episode_details:
                qparams = ['INACTIVE',rec.episode_seq]
                self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_EPISODE_STATUS",qparams)

                qparams = ['ACTIVE',rec.episode_seq]
                clips = self.connDB.execute_prepared_stmt("sotrueappplay","GET_EPISODE_CLIPS",qparams)
                if clips:
                    for clip in clips:
                        qparams = ["INACTIVE",clip.post_seq]
                        self.connDB.execute_prepared_stmt("sotrueappplay","SET_CLIP_STATUS",qparams)        

        response = self.create_response(dict(),"AE006")
        return(response)


    def get_all_shows(self):
        qparams = [self.session.get_session_value("_user_seq"),'ACTIVE']
        show_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_ALL_SHOWS_USER",qparams)
        if not show_data:
            response = self.create_error("AE005")
            return(response) 
        
        show_data = self.utils.convert_tuples_to_dicts(show_data)
        for rec in show_data:
            qparams = [rec["show_seq"]]
            show_topics = self.connDB.execute_prepared_stmt("sotrueappplay","GET_SHOW_TOPICS",qparams)
            topics = []
            for topic in show_topics:
                topics.append(topic)
            rec["topics"] = topics
            generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
            rec["logo_file"] = rec["logo_file"] if not rec["logo_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["logo_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])
            rec["thumb_file"] = rec["thumb_file"] if not rec["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["thumb_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])
            rec["preview_file"] = rec["preview_file"] if not rec["preview_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["preview_file"],AppConfigs.s3_posts_folder,rec["s3_enabled"])
                
        response = self.create_response(show_data,"AE004")
        return(response)


    def get_location_list(self):
        locations = ["Ranga Shankara, Bengaluru","Medai - The Stage Bengaluru","Vyoma Artspace, Bangaluru"]
        response = self.create_response(locations,"AE004")
        return(response)


    def add_season(self):

        cover_image = None 
        banner_image = None
        if self.params["uploads"] is not None:            
            if self.params["uploads"][0]["key"] == "cover_file":
                cover_image = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "banner_file":
                banner_image = self.params["uploads"][0]["upload_name"]

            if len(self.params["uploads"]) > 1:
                if self.params["uploads"][1]["key"] == "cover_file":
                    cover_image = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "banner_file":
                    banner_image = self.params["uploads"][1]["upload_name"]

        if cover_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + cover_image)
                cover_image = cover_image[0:cover_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + cover_image[cover_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + cover_image, file_name, AppConfigs.s3_posts_folder)
                    cover_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + cover_image,SotrueAppConfig.media_path + cover_image)            
                    self.cache_uploaded_file("cover_image",SotrueAppConfig.media_path + cover_image)
                self.utils.delete_file(self.configs.temp_folder_path + cover_image)

        if banner_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + banner_image)
                banner_image = banner_image[0:banner_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + banner_image[banner_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + banner_image, file_name, AppConfigs.s3_posts_folder)
                    banner_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + banner_image,SotrueAppConfig.media_path + banner_image)            
                    self.cache_uploaded_file("banner_image",SotrueAppConfig.media_path + banner_image)
                self.utils.delete_file(self.configs.temp_folder_path + banner_image)
                
        qparams = ['CLOSED',self.params["show_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","INVALIDATE_SEASON",qparams)
        
        qparams = [self.params["show_seq"],self.params["title"],self.params["description"],'ACTIVE',
                   cover_image, banner_image,
                   self.params["release_year"] if "release_year" in self.params else None,
                   self.params["grid_title"]]
        season_seq = self.connDB.execute_prepared_stmt("sotrueappplay","INSERT_SEASON",qparams)
        response = self.create_response(dict(season_seq=season_seq),"AE015")
        return(response)


    def get_season(self):
        qparams = [self.params["season_seq"]]
        season = self.connDB.execute_prepared_stmt("sotrueappplay","GET_SEASON",qparams)
        if not season:
            response = self.create_error("AE005")
            return(response)         
        season = self.utils.convert_tuples_to_dicts(season)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        season[0]["cover_image"] = season[0]["cover_image"] if not season[0]["cover_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + season[0]["cover_image"],AppConfigs.s3_posts_folder,"YES")        
        season[0]["banner_image"] = season[0]["banner_file"] if not season[0]["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + season[0]["banner_file"],AppConfigs.s3_posts_folder,"YES")        

        response = self.create_response(season,"AE004")
        return(response)


    def update_season(self):
        
        cover_image = None 
        banner_image = None
        if self.params["uploads"] is not None:            
            if self.params["uploads"][0]["key"] == "cover_file":
                cover_image = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "banner_file":
                banner_image = self.params["uploads"][0]["upload_name"]

            if len(self.params["uploads"]) > 1:
                if self.params["uploads"][1]["key"] == "cover_file":
                    cover_image = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "banner_file":
                    banner_image = self.params["uploads"][1]["upload_name"]

        if cover_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + cover_image)
                cover_image = cover_image[0:cover_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + cover_image[cover_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + cover_image, file_name, AppConfigs.s3_posts_folder)
                    cover_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + cover_image,SotrueAppConfig.media_path + cover_image)            
                    self.cache_uploaded_file("cover_image",SotrueAppConfig.media_path + cover_image)
                self.utils.delete_file(self.configs.temp_folder_path + cover_image)

        if banner_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + banner_image)
                banner_image = banner_image[0:banner_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + banner_image[banner_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + banner_image, file_name, AppConfigs.s3_posts_folder)
                    banner_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + banner_image,SotrueAppConfig.media_path + banner_image)            
                    self.cache_uploaded_file("banner_image",SotrueAppConfig.media_path + banner_image)
                self.utils.delete_file(self.configs.temp_folder_path + banner_image)

        qparams = [self.params["season_seq"]]
        season_details = self.connDB.execute_prepared_stmt("sotrueappplay","GET_SEASON",qparams)

        qparams = [self.params["title"],self.params["description"],
                   cover_image if cover_image else season_details[0].cover_image,
                   banner_image if banner_image else season_details[0].banner_file,
                   self.params["release_year"] if "release_year" in self.params else None,
                   self.params["grid_title"],self.params["season_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_SEASON",qparams)

        qparams = ['PENDING',None,None,self.params["season_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","RESET_SEASON_APPROVAL",qparams)

        response = self.create_response(dict(),"AE006")
        return(response)


    def set_season_status(self):
        qparams = [self.params["status"],self.params["season_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_SEASON_STATUS",qparams)

        qparams = [self.params["season_seq"],'ACTIVE']        
        episode_details = self.connDB.execute_prepared_stmt("sotrueappplay","GET_ALL_EPISODES_SEASON",qparams)
        if episode_details:
            for rec in episode_details:
                qparams = ['INACTIVE',rec.episode_seq]
                self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_EPISODE_STATUS",qparams)

                qparams = ['ACTIVE',rec.episode_seq]
                clips = self.connDB.execute_prepared_stmt("sotrueappplay","GET_EPISODE_CLIPS",qparams)
                if clips:
                    for clip in clips:
                        qparams = ["INACTIVE",clip.post_seq]
                        self.connDB.execute_prepared_stmt("sotrueappplay","SET_CLIP_STATUS",qparams)        

        response = self.create_response(dict(),"AE006")
        return(response)


    def get_all_seasons(self):
        qparams = [self.params["show_seq"],'ACTIVE']
        seasons = self.connDB.execute_prepared_stmt("sotrueappplay","GET_ALL_SEASONS",qparams)
        if not seasons:
            response = self.create_error("AE005")
            return(response) 
        seasons = self.utils.convert_tuples_to_dicts(seasons)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for rec in seasons:        
            rec["cover_image"] = rec["cover_image"] if not rec["cover_image"] else generic_code.create_media_url(SotrueAppConfig.media_path + rec["cover_image"],AppConfigs.s3_posts_folder,"YES")
        response = self.create_response(seasons,"AE004")
        return(response)


    def add_episode(self):
        
        thumb_image = None 
        preview_image = None
        banner_image = None
        if self.params["uploads"] is not None:            
            if self.params["uploads"][0]["key"] == "thumb_file":
                thumb_image = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "preview_file":
                preview_image = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "banner_file":
                banner_image = self.params["uploads"][0]["upload_name"]

            if len(self.params["uploads"]) > 1:
                if self.params["uploads"][1]["key"] == "thumb_file":
                    thumb_image = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "preview_file":
                    preview_image = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "banner_file":
                    banner_image = self.params["uploads"][1]["upload_name"]

            if len(self.params["uploads"]) > 2:
                if self.params["uploads"][2]["key"] == "thumb_file":
                    thumb_image = self.params["uploads"][2]["upload_name"]
                elif self.params["uploads"][2]["key"] == "preview_file":
                    preview_image = self.params["uploads"][2]["upload_name"]
                elif self.params["uploads"][2]["key"] == "banner_file":
                    banner_image = self.params["uploads"][2]["upload_name"]
                
            if not thumb_image:
                response = self.create_error("AE012")
                return(response)
            
            if thumb_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + thumb_image)
                thumb_image = thumb_image[0:thumb_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + thumb_image[thumb_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + thumb_image, file_name, AppConfigs.s3_posts_folder)
                    thumb_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + thumb_image,SotrueAppConfig.media_path + thumb_image)            
                    self.cache_uploaded_file("thumb_image",SotrueAppConfig.media_path + thumb_image)
                self.utils.delete_file(self.configs.temp_folder_path + thumb_image)

            if preview_image:                                
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + preview_image[preview_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + preview_image, file_name, AppConfigs.s3_posts_folder)
                    preview_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + preview_image,SotrueAppConfig.media_path + preview_image)            
                    self.cache_uploaded_file("preview_image",SotrueAppConfig.media_path + preview_image)
                self.utils.delete_file(self.configs.temp_folder_path + preview_image)

            if banner_image:                                
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + banner_image[banner_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + banner_image, file_name, AppConfigs.s3_posts_folder)
                    banner_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + banner_image,SotrueAppConfig.media_path + banner_image)            
                    self.cache_uploaded_file("banner_image",SotrueAppConfig.media_path + banner_image)
                self.utils.delete_file(self.configs.temp_folder_path + banner_image)
        else:
            response = self.create_error("AE012")
            return(response)

        qparams = [self.params["title"],self.params["description"],self.params["is_paid"],
                    self.params["price"] if "price" in self.params else None,
                    self.params["location"] if "location" in self.params else None,
                    self.params["expiry_date"] if "expiry_date" in self.params else None,
                    self.params["schedule_on"] if "schedule_on" in self.params else None,
                    thumb_image, preview_image, banner_image, 'ACTIVE', self.params["show_seq"],
                    self.params["season_seq"] if "season_seq" in self.params else -1,                    
                    'YES' if AppConfigs.s3_enabled else 'NO',
                    self.params["release_year"] if "release_year" in self.params else None,
                    self.params["grid_title"]]
        episode_seq = self.connDB.execute_prepared_stmt("sotrueappplay","INSERT_EPISODE",qparams)

        if "reactions" in self.params:
            reactions = self.utils.parse_json(self.params["reactions"])
            for reaction in reactions:
                qparams = [episode_seq,reaction]
                reaction_seq = self.connDB.execute_prepared_stmt("sotrueappplay","INSERT_EPISODE_REACTIONS",qparams)

        if "people_tags" in self.params:
            people_tags = self.utils.parse_json(self.params["people_tags"])
            for tag in people_tags:
                qparams = [self.session.get_session_value("_user_seq"),tag,self.utils.get_cur_timestamp(),
                           episode_seq,"EPISODE"]
                tag_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST_TAG",qparams)

        if self.session.get_session_value("_email_id"):
            mailer = AppEmail()
            mailer.send_email(self.session.get_session_value("_email_id"),None,"SoTrue Playlist Submission",
                "Hey there!\n\nThank you for submitting your playlist to us! We appreciate your effort and enthusiasm in sharing your content with our community.\n\nOur team will review your playlist within the next 24-48 hours. Once the review is complete, we will get back to you with feedback or next steps.\n\nIf you have any questions in the meantime, please feel free to reach out to us.\n\nBest,\nTeam SoTrue\n");        

        response = self.create_response(dict(episode_seq=episode_seq),"AE016")
        self.session.set_session_value("_last_seq",episode_seq)
        return(response)


    def get_episode(self):
        qparams = [self.params["episode_seq"]]
        episode_details = self.connDB.execute_prepared_stmt("sotrueappplay","GET_EPISODE_DETAILS",qparams)
        if not episode_details:
            response = self.create_error("AE005")
            return(response)     
        reaction_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_EPISODE_REACTIONS",qparams)
        reactions = []
        for reaction in reaction_data:
            reactions.append(reaction)

        qparams = [self.params["episode_seq"]]
        tagged_users = self.connDB.execute_prepared_stmt("sotrueappplay","GET_TAGGED_USERS",qparams)

        tags = []
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)            
        for tag in tagged_users:
            row = {"tag_seq":tag.tag_seq,"profile_seq":tag.profile_seq,
                   "profile_picture":tag.profile_picture if not tag.profile_picture else generic_code.create_media_url(SotrueAppConfig.profile_path + tag.profile_picture,AppConfigs.s3_profiles_folder,tag.s3_enabled)}
            tags.append(row)

        episode_details = self.utils.convert_tuples_to_dicts(episode_details)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        episode_details[0]["thumb_file"] = episode_details[0]["thumb_file"] if not episode_details[0]["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + episode_details[0]["thumb_file"],AppConfigs.s3_posts_folder,episode_details[0]["s3_enabled"])        
        episode_details[0]["preview_file"] = episode_details[0]["preview_file"] if not episode_details[0]["preview_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + episode_details[0]["preview_file"],AppConfigs.s3_posts_folder,episode_details[0]["s3_enabled"])        
        episode_details[0]["banner_file"] = episode_details[0]["banner_file"] if not episode_details[0]["banner_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + episode_details[0]["banner_file"],AppConfigs.s3_posts_folder,episode_details[0]["s3_enabled"])        
        
        episode_details[0]["reactions"] = reactions
        episode_details[0]["tags"] = tags
        response = self.create_response(episode_details,"AE004")
        return(response)


    def update_episode(self):
        qparams = [self.params["episode_seq"]]
        episode_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_EPISODE_DETAILS",qparams)

        if episode_data[0].schedule and self.utils.get_cur_timestamp()>str(episode_data[0].schedule):
            response = self.create_error("AE017")
            return(response)

        thumb_image = None 
        preview_image = None
        banner_image = None
        if self.params["uploads"] is not None:            
            if self.params["uploads"][0]["key"] == "thumb_file":
                thumb_image = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "preview_file":
                preview_image = self.params["uploads"][0]["upload_name"]
            elif self.params["uploads"][0]["key"] == "banner_file":
                banner_image = self.params["uploads"][0]["upload_name"]

            if len(self.params["uploads"]) > 1:
                if self.params["uploads"][1]["key"] == "thumb_file":
                    thumb_image = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "preview_file":
                    preview_image = self.params["uploads"][1]["upload_name"]
                elif self.params["uploads"][1]["key"] == "banner_file":
                    banner_image = self.params["uploads"][1]["upload_name"]
            
            if len(self.params["uploads"]) > 2:
                if self.params["uploads"][2]["key"] == "thumb_file":
                    thumb_image = self.params["uploads"][2]["upload_name"]
                elif self.params["uploads"][2]["key"] == "preview_file":
                    preview_image = self.params["uploads"][2]["upload_name"]
                elif self.params["uploads"][2]["key"] == "banner_file":
                    banner_image = self.params["uploads"][2]["upload_name"]
            
            #if not preview_image or not thumb_image:
            #    response = self.create_error("AE012")
            #    return(response)
            
            if thumb_image:
                resize_extn = self.utils.resize_image(self.configs.temp_folder_path + thumb_image)
                thumb_image = thumb_image[0:thumb_image.rfind(".")+1] + resize_extn
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + thumb_image[thumb_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + thumb_image, file_name, AppConfigs.s3_posts_folder)
                    thumb_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + thumb_image,SotrueAppConfig.media_path + thumb_image)            
                    self.cache_uploaded_file("thumb_image",SotrueAppConfig.media_path + thumb_image)
                self.utils.delete_file(self.configs.temp_folder_path + thumb_image)

            if preview_image:                
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + preview_image[preview_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + preview_image, file_name, AppConfigs.s3_posts_folder)
                    preview_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + preview_image,SotrueAppConfig.media_path + preview_image)            
                    self.cache_uploaded_file("preview_image",SotrueAppConfig.media_path + preview_image)
                self.utils.delete_file(self.configs.temp_folder_path + preview_image)                

            if banner_image:                
                if AppConfigs.s3_enabled:
                    file_name = self.utils.get_uuid_str() + banner_image[banner_image.rfind("."):]
                    awsS3 = AppAwsS3()
                    awsS3.send_media_file(self.configs.temp_folder_path + banner_image, file_name, AppConfigs.s3_posts_folder)
                    banner_image = file_name
                else:
                    self.utils.copy_files(self.configs.temp_folder_path + banner_image,SotrueAppConfig.media_path + banner_image)            
                    self.cache_uploaded_file("banner_image",SotrueAppConfig.media_path + banner_image)
                self.utils.delete_file(self.configs.temp_folder_path + banner_image)

        qparams = [self.params["title"],self.params["description"],
                    self.params["price"] if "price" in self.params else None,
                    self.params["location"] if "location" in self.params else None,
                    self.params["expiry_date"] if "expiry_date" in self.params else None,
                    self.params["schedule_on"] if "schedule_on" in self.params else None,
                    thumb_image if thumb_image else episode_data[0].thumb_file,
                    preview_image if preview_image else episode_data[0].preview_file,
                    banner_image if banner_image else episode_data[0].banner_file,
                    self.params["release_year"] if "release_year" in self.params else None,
                    self.params["grid_title"], self.params["episode_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_EPISODE",qparams)

        qparams = ['PENDING',None,None,None,self.params["episode_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","RESET_EPISODE_APPROVAL",qparams)

        if "reactions" in self.params:
            qparams = [self.params["episode_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappplay","DELETE_EPISODE_REACTIONS",qparams)

            reactions = self.utils.parse_json(self.params["reactions"])
            for reaction in reactions:
                qparams = [self.params["episode_seq"],reaction]
                reaction_seq = self.connDB.execute_prepared_stmt("sotrueappplay","INSERT_EPISODE_REACTIONS",qparams)

        if "people_tags" in self.params:
            qparams = [self.session.get_session_value("_user_seq"),'EPISODE',self.params["episode_seq"]]
            self.connDB.execute_prepared_stmt("sotrueappuser","REMOVE_POST_TAG",qparams)

            people_tags = self.utils.parse_json(self.params["people_tags"])
            for tag in people_tags:
                qparams = [self.session.get_session_value("_user_seq"),tag,self.utils.get_cur_timestamp(),
                           self.params["episode_seq"],"EPISODE"]
                tag_seq = self.connDB.execute_prepared_stmt("sotrueappuser","INSERT_POST_TAG",qparams)

        response = self.create_response(dict(),"AE006")        
        return(response)


    def set_episode_status(self):
        qparams = [self.params["status"],self.params["episode_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_EPISODE_STATUS",qparams)

        qparams = ['ACTIVE',self.params["episode_seq"]]
        clips = self.connDB.execute_prepared_stmt("sotrueappplay","GET_EPISODE_CLIPS",qparams)
        if clips:
            for clip in clips:
                qparams = ["INACTIVE",clip.post_seq]
                self.connDB.execute_prepared_stmt("sotrueappplay","SET_CLIP_STATUS",qparams)      
                
        response = self.create_response(dict(),"AE006")
        return(response)


    def get_all_episodes(self):
        qparams = [self.params["show_seq"],'ACTIVE']
        subs = {"<SEASON_QUERY>":(" AND season_seq=" + str(self.params["season_seq"])) if "season_seq" in self.params else ""}
        episode_details = self.connDB.execute_prepared_stmt("sotrueappplay","GET_ALL_EPISODES",qparams,subs=subs)
        if not episode_details:
            response = self.create_error("AE005")
            return(response) 

        episode_details = self.utils.convert_tuples_to_dicts(episode_details)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)
        for episode in episode_details:
            episode["thumb_file"] = episode["thumb_file"] if not episode["thumb_file"] else generic_code.create_media_url(SotrueAppConfig.media_path + episode["thumb_file"],AppConfigs.s3_posts_folder,episode["s3_enabled"])        
                
        response = self.create_response(episode_details,"AE004")
        return(response)


    def get_clips(self):
        qparams = ['ACTIVE',self.params["episode_seq"]]
        clips = self.connDB.execute_prepared_stmt("sotrueappplay","GET_EPISODE_CLIPS",qparams)
        print("clips found =>",clips)
        if not clips:
            response = self.create_error("AE005")
            return(response) 
        clips = self.utils.convert_tuples_to_dicts(clips)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)    
        for clip in clips:
            clip["media_file"] = generic_code.create_media_url(SotrueAppConfig.media_path +  clip["media_file"],AppConfigs.s3_posts_folder,clip["s3_enabled"])

        response = self.create_response(clips,"AE004")
        return(response)


    def set_clip_status(self):
        qparams = [self.params["status"],self.params["post_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","SET_CLIP_STATUS",qparams)
        response = self.create_response(dict(),"AE006")
        return(response)


    def update_clip(self):
        qparams = [self.params["comments"],self.params["post_seq"]]
        self.connDB.execute_prepared_stmt("sotrueappplay","UPDATE_CLIP_COMMENT",qparams)
        qparams = [self.params["post_seq"]]
        episode = self.connDB.execute_prepared_stmt("sotrueappplay","GET_EPISODE_FOR_CLIP",qparams)
        if episode:
            qparams = ['PENDING',None,None,None,episode[0].episode_seq]
            self.connDB.execute_prepared_stmt("sotrueappplay","RESET_EPISODE_APPROVAL",qparams)

        response = self.create_response(dict(),"AE006")
        return(response)


    def get_seasons_list(self):
        qparams = [self.params["show_seq"],'ACTIVE','PENDING']
        season_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_SEASONS_LIST",qparams)
        if not season_data:
            response = self.create_error("AE005")
            return(response) 
        
        season_data = self.utils.convert_tuples_to_dicts(season_data)
        response = self.create_response(season_data,"AE004")
        return(response)


    def get_shows_list(self):
        qparams = [self.session.get_session_value("_user_seq"),'ACTIVE']
        show_data = self.connDB.execute_prepared_stmt("sotrueappplay","GET_SHOWS_LIST",qparams)
        if not show_data:
            response = self.create_error("AE005")
            return(response) 
        
        show_data = self.utils.convert_tuples_to_dicts(show_data)
        response = self.create_response(show_data,"AE004")
        return(response)


    def get_master_banner(self):
        qparams = [self.session.get_session_value("_user_seq")]
        master_banner = self.connDB.execute_prepared_stmt("sotrueappplay","GET_PLAYLIST_MASTER",qparams)

        if not master_banner or not master_banner[0].playlist_master_image:
            response = self.create_error("AE005")
            return(response) 

        master_banner = self.utils.convert_tuples_to_dicts(master_banner)
        generic_code = SotrueAppGenericCode(self.connDB,self.session,self.utils,self.logger)           
        master_banner[0]["playlist_master_image"] = generic_code.create_media_url(SotrueAppConfig.media_path + master_banner[0]["playlist_master_image"],AppConfigs.s3_posts_folder,"YES")

        response = self.create_response(master_banner,"AE004")
        return(response)


    def get_release_years(self):
        start_year = 2020
        ts = self.utils.get_cur_timestamp()
        year = int(ts[0:ts.find("-")])
        years_list = []
        while start_year <= year:
            years_list.append(start_year)
            start_year += 1

        response = self.create_response(years_list,"AE004")
        return(response)