# SoTrue Backend Performance Optimization - Executable Task Document

## Executive Summary

This document provides a comprehensive analysis of performance bottlenecks across the SoTrue Backend application and actionable optimization tasks for immediate implementation. The analysis reveals systemic N+1 query problems, lack of caching, and inefficient data processing patterns affecting multiple critical endpoints.

## Critical Performance Issues Identified

### **1. GET_POSTS - CRITICAL (Analyzed in Detail)**
**Impact**: Core user experience - home feed loading
**Current Performance**: 5-15 seconds per request
**Database Queries**: 50+ per request
**Priority**: IMMEDIATE

### **2. GET_POSTS_USER - HIGH**
**Impact**: User profile page loading
**Database Queries**: 25+ per request (similar pattern to GET_POSTS)
**Issues Found**:
- Same reaction processing loop (17 reactions x 2 queries = 34 queries)
- Multiple post metadata queries
- Profile data fetching without caching

### **3. GET_BOOKMARKS - HIGH**
**Impact**: User bookmarks page
**Database Queries**: 20+ per request
**Issues Found**:
- Identical reaction processing pattern
- Same post enrichment queries as GET_POSTS
- No caching of bookmark data

### **4. DO_SEARCH - MEDIUM**
**Impact**: Search functionality
**Database Queries**: 8-12 per request
**Issues Found**:
- Multiple search queries with fallbacks
- Profile data fetching for each result
- No search result caching

### **5. GET_NOTIFICATIONS_* Methods - MEDIUM**
**Impact**: Notification system
**Database Queries**: 5-10 per request each
**Issues Found**:
- Separate queries for each notification type
- Profile data fetching for each notification
- No notification caching

## Systemic Performance Anti-Patterns

### **1. Reaction Processing Anti-Pattern**
**Found in**: GET_POSTS, GET_POSTS_USER, GET_BOOKMARKS, and others
```python
# PROBLEMATIC CODE PATTERN (repeated across multiple methods):
for react in self.reactions:  # 17 iterations
    query_params = [profile_seq, react]
    post_reactions = self.connDB.execute_prepared_stmt("GET_USER_POST_LIKES", query_params, subs=subs)
    
    query_params = [react]
    reaction_counts = self.connDB.execute_prepared_stmt("GET_POST_LIKE_COUNTS", query_params, subs=subs)
```
**Impact**: 34 additional database queries per method call

### **2. Post Enrichment Anti-Pattern**
**Found in**: All post-related methods
```python
# PROBLEMATIC CODE PATTERN:
post_bookmarks = self.connDB.execute_prepared_stmt("GET_POST_BOOKMARKS", qparams, subs=subs)
subscribed_posts = self.connDB.execute_prepared_stmt("CHECK_POST_SUBSCRIPION_LIST", qparams, subs=subs)
post_comments = self.connDB.execute_prepared_stmt("GET_USER_POST_COMMENTS", qparams, subs=subs)
post_tags = self.connDB.execute_prepared_stmt("GET_POST_TAG_LIST", None, subs=subs)
post_reactions = self.connDB.execute_prepared_stmt("GET_POST_REACTIONS_LIST", None, subs=subs)
# ... 10+ more similar queries
```

### **3. Profile Data Fetching Anti-Pattern**
**Found in**: All methods that display user information
```python
# PROBLEMATIC CODE PATTERN:
profile_data = self.connDB.execute_prepared_stmt("GET_PROFILE_FOR_USERS", None, subs=subs)
# No caching, fetched on every request
```

### **4. Browsing History Anti-Pattern**
**Found in**: GET_POSTS and related methods
```python
# PROBLEMATIC SCHEMA DESIGN:
browsing_history (profile_seq, content_seqs VARCHAR(8192))  # "1,2,3,4,5,..."
# Should be: browsing_history_items (profile_seq, post_seq, viewed_at)
```

## Executable Optimization Tasks

### **PHASE 1: IMMEDIATE FIXES (1-2 Days)**

#### **Task 1.1: Implement Redis Caching Infrastructure**
**Priority**: CRITICAL
**Estimated Time**: 4 hours
**Files to Modify**:
- `sotruebackend/components/AppConfigs.py`
- `sotruebackend/components/AppDBManager.py`
- `requirements.txt`

**Implementation Steps**:
1. Add Redis dependency to requirements.txt
2. Create CacheManager class in components/
3. Add Redis configuration to AppConfigs
4. Integrate cache manager into AppDBManager

**Code Template**:
```python
# components/AppCacheManager.py
import redis
import json
from .AppConfigs import AppConfigs

class AppCacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(
            host=AppConfigs.redis_host,
            port=AppConfigs.redis_port,
            db=AppConfigs.redis_db
        )
    
    def get(self, key):
        try:
            value = self.redis_client.get(key)
            return value.decode('utf-8') if value else None
        except:
            return None
    
    def setex(self, key, ttl, value):
        try:
            self.redis_client.setex(key, ttl, value)
        except:
            pass  # Fail silently, don't break app if Redis is down
```

#### **Task 1.2: Optimize Reaction Processing**
**Priority**: CRITICAL
**Estimated Time**: 6 hours
**Files to Modify**:
- `sotruebackend/sotrueapp/application/handlers/SotrueAppUserHandler.py`
- `sotruebackend/sotrueapp/application/queries/sotrueappuser.txt`

**Implementation Steps**:
1. Create batch reaction query in sotrueappuser.txt
2. Replace reaction loops in all affected methods
3. Add reaction data caching (5-minute TTL)

**New SQL Query**:
```sql
<query>
    <id>GET_POST_REACTIONS_BATCH</id>
    <stmt>
        SELECT post_seq, reaction_type, 
               COUNT(*) as total_count,
               SUM(CASE WHEN liked_by_profile = %s THEN 1 ELSE 0 END) as user_count
        FROM post_likes 
        WHERE post_seq IN (<POST_LIST>) AND status = 'ACTIVE'
        GROUP BY post_seq, reaction_type
    </stmt>
    <dbkey>sotruedb</dbkey>
    <dbschema>sotrueschema</dbschema>
</query>
```

#### **Task 1.3: Remove Unused Queries**
**Priority**: HIGH
**Estimated Time**: 2 hours
**Files to Modify**:
- `sotruebackend/sotrueapp/application/handlers/SotrueAppUserHandler.py`

**Implementation Steps**:
1. Remove GET_POSTS_COUNT query from get_posts method (line 1944)
2. Remove GET_USER_POSTS_COUNT from get_posts_user method
3. Remove other unused count queries

#### **Task 1.4: Add Connection Pooling**
**Priority**: HIGH
**Estimated Time**: 4 hours
**Files to Modify**:
- `sotruebackend/components/AppDBManager.py`

**Implementation Steps**:
1. Replace direct psycopg2 connections with connection pooling
2. Add pool configuration to AppConfigs
3. Implement connection reuse logic

### **PHASE 2: QUERY OPTIMIZATION (3-5 Days)**

#### **Task 2.1: Create Optimized Batch Queries**
**Priority**: CRITICAL
**Estimated Time**: 8 hours
**Files to Modify**:
- `sotruebackend/sotrueapp/application/queries/sotrueappuser.txt`

**New Queries to Create**:
1. `GET_POSTS_WITH_PROFILE_DATA` - Single query with JOINs
2. `GET_USER_POST_INTERACTIONS_BATCH` - All user interactions in one query
3. `GET_POST_ENGAGEMENT_COUNTS_BATCH` - All engagement metrics in one query
4. `GET_PROFILE_DATA_BATCH` - Optimized profile fetching

#### **Task 2.2: Implement Profile Data Caching**
**Priority**: HIGH
**Estimated Time**: 6 hours
**Files to Modify**:
- `sotruebackend/sotrueapp/application/handlers/SotrueAppUserHandler.py`

**Implementation Steps**:
1. Cache profile data for 30 minutes
2. Implement batch profile fetching with cache fallback
3. Add cache invalidation on profile updates

**Code Template**:
```python
def get_cached_profile_data(self, profile_ids):
    cache_key = f"profiles:batch:{hash(tuple(sorted(profile_ids)))}"
    cached_data = self.cache_manager.get(cache_key)
    
    if cached_data:
        return json.loads(cached_data)
    
    # Fetch from database
    profile_data = self.fetch_profile_data_batch(profile_ids)
    
    # Cache for 30 minutes
    self.cache_manager.setex(cache_key, 1800, json.dumps(profile_data))
    
    return profile_data
```

#### **Task 2.3: Optimize Browsing History**
**Priority**: MEDIUM
**Estimated Time**: 4 hours
**Files to Modify**:
- `sotruebackend/sotrueapp/application/handlers/SotrueAppUserHandler.py`

**Implementation Steps**:
1. Implement efficient browsing history caching
2. Limit browsing history to last 1000 items
3. Use Redis for browsing history storage

### **PHASE 3: SCHEMA IMPROVEMENTS (1-2 Weeks)**

#### **Task 3.1: Database Index Analysis and Creation**
**Priority**: HIGH
**Estimated Time**: 16 hours

**Indexes to Create**:
```sql
-- Critical indexes for performance
CREATE INDEX idx_user_posts_profile_status_posted ON user_posts(profile_seq, status, posted_on DESC);
CREATE INDEX idx_user_posts_expire_status ON user_posts(expire_on, status);
CREATE INDEX idx_post_likes_post_user_type ON post_likes(post_seq, liked_by_profile, type);
CREATE INDEX idx_user_profile_status_verified ON user_profile(status, is_verified);
CREATE INDEX idx_profile_followers_profile_status ON profile_followers(profile_seq, status);

-- Composite indexes for complex queries
CREATE INDEX idx_user_posts_complex ON user_posts(profile_seq, status, is_playlist, expire_on, posted_on DESC);
CREATE INDEX idx_post_content_type_status ON user_post_content(media_type, status);
```

#### **Task 3.2: Browsing History Schema Redesign**
**Priority**: MEDIUM
**Estimated Time**: 12 hours

**New Schema**:
```sql
-- Replace comma-separated string with proper table
CREATE TABLE browsing_history_items (
    history_item_id SERIAL PRIMARY KEY,
    profile_seq INTEGER NOT NULL,
    post_seq INTEGER NOT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_seq) REFERENCES user_profile(profile_seq),
    FOREIGN KEY (post_seq) REFERENCES user_posts(post_seq)
);

CREATE INDEX idx_browsing_history_profile_viewed ON browsing_history_items(profile_seq, viewed_at DESC);
```

### **PHASE 4: ADVANCED OPTIMIZATIONS (2-3 Weeks)**

#### **Task 4.1: Implement Search Result Caching**
**Priority**: MEDIUM
**Estimated Time**: 8 hours

#### **Task 4.2: Add Database Read Replicas**
**Priority**: LOW
**Estimated Time**: 16 hours

#### **Task 4.3: Implement CDN for Media URLs**
**Priority**: LOW
**Estimated Time**: 12 hours

## Performance Monitoring Implementation

### **Task M.1: Add Performance Metrics**
**Files to Create**:
- `sotruebackend/components/AppPerformanceMonitor.py`

**Metrics to Track**:
- Database query count per request
- Response time per endpoint
- Cache hit/miss rates
- Database connection pool utilization

**Code Template**:
```python
import time
from functools import wraps

class AppPerformanceMonitor:
    def __init__(self):
        self.metrics = {}
    
    def track_endpoint_performance(self, endpoint_name):
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.time()
                query_count_before = self.get_query_count()
                
                result = func(*args, **kwargs)
                
                execution_time = time.time() - start_time
                query_count = self.get_query_count() - query_count_before
                
                self.log_metrics(endpoint_name, execution_time, query_count)
                return result
            return wrapper
        return decorator
```

## Expected Performance Improvements

### **After Phase 1 (Immediate Fixes)**:
- **GET_POSTS**: 50+ queries to 15 queries (70% reduction)
- **Response Time**: 5-15s to 2-5s (60% improvement)
- **Cache Hit Rate**: 0% to 40%

### **After Phase 2 (Query Optimization)**:
- **GET_POSTS**: 15 queries to 8 queries (85% total reduction)
- **Response Time**: 2-5s to 500ms-1s (90% total improvement)
- **Cache Hit Rate**: 40% to 70%

### **After Phase 3 (Schema Improvements)**:
- **Database Load**: 70% reduction
- **Query Performance**: 50% faster individual queries
- **Scalability**: 300% improvement

## Risk Assessment and Mitigation

### **High Risk Tasks**:
- Schema changes (Task 3.2)
- Core query modifications (Task 2.1)

### **Mitigation Strategies**:
1. **Feature Flags**: Implement toggles for new vs old implementations
2. **Gradual Rollout**: Deploy to 10% of users first
3. **Rollback Plan**: Keep old code paths available
4. **Load Testing**: Test with production-like data volumes

## Success Criteria

### **Phase 1 Success Metrics**:
- [ ] Redis caching operational
- [ ] Reaction queries reduced by 80%
- [ ] Connection pooling implemented
- [ ] Response time improved by 50%

### **Phase 2 Success Metrics**:
- [ ] Database queries per request < 10
- [ ] 95th percentile response time < 1 second
- [ ] Cache hit rate > 60%

### **Phase 3 Success Metrics**:
- [ ] Database query performance improved by 50%
- [ ] System supports 100+ concurrent users
- [ ] Zero performance regressions

## Implementation Priority Matrix

| Task | Impact | Effort | Priority | Dependencies |
|------|--------|--------|----------|--------------|
| 1.1 Redis Caching | HIGH | LOW | 1 | None |
| 1.2 Reaction Optimization | HIGH | MEDIUM | 2 | 1.1 |
| 1.3 Remove Unused Queries | MEDIUM | LOW | 3 | None |
| 1.4 Connection Pooling | HIGH | MEDIUM | 4 | None |
| 2.1 Batch Queries | HIGH | HIGH | 5 | 1.1, 1.2 |
| 2.2 Profile Caching | MEDIUM | MEDIUM | 6 | 1.1 |
| 2.3 Browsing History | MEDIUM | MEDIUM | 7 | 1.1 |
| 3.1 Database Indexes | HIGH | HIGH | 8 | None |
| 3.2 Schema Redesign | MEDIUM | HIGH | 9 | 3.1 |

## Execution Timeline

### **Week 1**: Phase 1 Implementation
- Days 1-2: Tasks 1.1, 1.3
- Days 3-4: Task 1.2
- Day 5: Task 1.4

### **Week 2**: Phase 2 Planning and Start
- Days 1-3: Task 2.1 (Batch Queries)
- Days 4-5: Task 2.2 (Profile Caching)

### **Week 3**: Phase 2 Completion
- Days 1-2: Task 2.3 (Browsing History)
- Days 3-5: Testing and optimization

### **Weeks 4-5**: Phase 3 Implementation
- Week 4: Task 3.1 (Database Indexes)
- Week 5: Task 3.2 (Schema Redesign)

## Monitoring and Alerting Setup

### **Critical Alerts**:
- Response time > 2 seconds for 5 minutes
- Database query count > 20 per request
- Cache hit rate < 40% for 10 minutes
- Database connection pool > 80% utilization

### **Performance Dashboards**:
- Real-time response time graphs
- Database query count trends
- Cache performance metrics
- User experience impact metrics

## Affected Endpoints Summary

### **Critical Priority (Immediate Action Required)**:
1. **GET_POSTS** - Home feed (50+ queries)
2. **GET_POSTS_USER** - User profile (25+ queries)
3. **GET_BOOKMARKS** - User bookmarks (20+ queries)

### **High Priority**:
4. **GET_STORIES** - Stories feed (15+ queries)
5. **GET_FOLLOWERS** - Follower list (10+ queries)
6. **GET_FOLLOWING** - Following list (10+ queries)

### **Medium Priority**:
7. **DO_SEARCH** - Search functionality (8-12 queries)
8. **GET_NOTIFICATIONS_LIKES** - Like notifications (5-8 queries)
9. **GET_NOTIFICATIONS_COMMENTS** - Comment notifications (5-8 queries)
10. **GET_NOTIFICATIONS_FOLLOWS** - Follow notifications (5-8 queries)

### **Low Priority**:
11. **GET_PROFILE_SUGGESTIONS** - Profile recommendations
12. **GET_SUBSCRIBED_POSTS** - Subscription management
13. **GET_PAYMENT_HISTORY** - Payment-related queries

---

**Document Version**: 1.0  
**Last Updated**: Current Date  
**Next Review**: After Phase 1 Completion  
**Owner**: Development Team  
**Stakeholders**: Product, DevOps, QA Teams

## Quick Start Guide for Developers

### **Immediate Actions (Today)**:
1. Set up Redis instance for caching
2. Identify the worst-performing endpoint in your environment
3. Implement Task 1.1 (Redis Caching Infrastructure)
4. Apply Task 1.3 (Remove unused queries) to get immediate wins

### **This Week**:
1. Complete Phase 1 tasks
2. Monitor performance improvements
3. Plan Phase 2 implementation

### **Next Week**:
1. Begin Phase 2 query optimization
2. Implement batch queries for the most critical endpoints
3. Add comprehensive performance monitoring

This document provides a complete roadmap for transforming the SoTrue Backend from a slow, query-heavy application to a high-performance, scalable system.