import boto3
import datetime
from pathlib import Path
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives import serialization
from cryptography.hazmat.primitives.asymmetric import padding
from botocore.signers import CloudFrontSigner
from .AppConfigs import AppConfigs
from .AppUtils import AppUtils
from .AppLogger import AppLogger

def rsa_signer(message):
    # Construct a path relative to this file's location
    pem_path = Path(__file__).resolve().parent / "../sotrue_cloudfront_private.pem"
    pem_path = pem_path.resolve()  # Normalize to an absolute path
    with open(pem_path, 'rb') as key_file:        
            private_key = serialization.load_pem_private_key(
            key_file.read(),
            password=None,
            backend=default_backend()
        )
    return private_key.sign(message, padding.PKCS1v15(), hashes.SHA1())

class AppAwsS3:
    def __init__(self):
        pass

    def send_media_file(self, file_path, file_name, folder):
        # First log the attempt
        AppLogger.log_message("Entering send_media_file method", "DEBUG")
        
        try:
            # Log params separately to avoid formatting issues
            AppLogger.log_message(f"file_path: {str(file_path)}", "DEBUG")
            AppLogger.log_message(f"file_name: {str(file_name)}", "DEBUG")
            AppLogger.log_message(f"folder: {str(folder)}", "DEBUG")
            
            # Create S3 client first to test connection
            import botocore
            AppLogger.log_message("Creating S3 client...", "DEBUG")
            
            s3_client = boto3.client(
                's3',
                aws_access_key_id=AppConfigs.aws_access_key_id,
                aws_secret_access_key=AppConfigs.aws_secret_access_key,
                region_name=AppConfigs.aws_region
            )
            
            # Check file existence
            import os
            if not os.path.exists(str(file_path)):
                error_msg = f"File does not exist: {str(file_path)}"
                AppLogger.log_message(error_msg, "ERROR")
                raise FileNotFoundError(error_msg)
            
            # Prepare upload path
            full_s3_path = str(folder) + str(file_name)
            AppLogger.log_message(f"Full S3 path: {full_s3_path}", "DEBUG")
            
            # Try the upload
            AppLogger.log_message("Starting file upload...", "DEBUG")
            try:
                with open(str(file_path), 'rb') as file_obj:
                    s3_client.upload_fileobj(
                        file_obj,
                        AppConfigs.s3_container,
                        full_s3_path
                    )
                AppLogger.log_message(f"Successfully uploaded {file_name}", "INFO")
            except botocore.exceptions.ClientError as e:
                error_msg = f"AWS Error: {str(e)}"
                AppLogger.log_message(error_msg, "ERROR")
                raise
            except IOError as e:
                error_msg = f"IO Error: {str(e)}"
                AppLogger.log_message(error_msg, "ERROR")
                raise
            
        except Exception as ex:
            AppLogger.log_message(f"Exception in send_media_file: {str(ex)}", "ERROR")
            AppLogger.log_exception(ex, "Failed in send_media_file")
            raise
    
    def get_media_file(self, download_path, file_name, folder):
        s3_resource = boto3.resource(
            's3',
            aws_access_key_id=AppConfigs.aws_access_key_id,
            aws_secret_access_key=AppConfigs.aws_secret_access_key,
            region_name=AppConfigs.aws_region
        )
        s3_resource.Bucket(AppConfigs.s3_container).download_file(folder + file_name, download_path)

    def get_media_file_url(self, file_name, folder):
        if AppConfigs.cloud_front_enabled:
            return self.get_media_file_url_cloud(file_name, folder)
        else:
            s3_client = boto3.client(
                's3',
                aws_access_key_id=AppConfigs.aws_access_key_id,
                aws_secret_access_key=AppConfigs.aws_secret_access_key,
                region_name=AppConfigs.aws_region
            )
            response = s3_client.generate_presigned_url(
                'get_object',
                Params={'Bucket': AppConfigs.s3_container, 'Key': folder + file_name},
                ExpiresIn=AppConfigs.s3_expiry
            )
            return response

    def get_media_file_url_cloud(self, file_name, folder):
        cloudfront_signer = CloudFrontSigner(AppConfigs.s3_key_id, rsa_signer)
        expire_date = AppUtils.get_ts_ahead_by_sec(AppUtils.get_cur_timestamp(), AppConfigs.s3_expiry)
        url = AppConfigs.cloud_front_url + folder + file_name
        signed_url = cloudfront_signer.generate_presigned_url(url, date_less_than=expire_date)
        return signed_url

if __name__ == "__main__":
    pass