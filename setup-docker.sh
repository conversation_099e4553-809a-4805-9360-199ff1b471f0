#!/bin/bash

# Create .env file if it doesn't exist
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example..."
    cp .env.example .env
    echo "Please review and update the .env file if needed."
else
    echo ".env file already exists."
fi

# Create log directory
echo "Creating log directory..."
mkdir -p /tmp/sotruelogs
chmod 777 /tmp/sotruelogs

# Build and start Docker containers
echo "Building and starting Docker containers..."
docker-compose up -d --build

# Show container status
echo "Container status:"
docker-compose ps

echo ""
echo "Setup complete! Your application should be running at http://localhost:8000"
echo ""
echo "Useful commands:"
echo "  - View logs: docker-compose logs -f"
echo "  - Stop containers: docker-compose down"
echo "  - Run Django commands: docker-compose exec web python manage.py [command]"
